#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的OBS诊断工具
"""

import obsws_python as obs
import sys

def main():
    print("🔧 OBS简化诊断工具")
    print("=" * 40)
    
    try:
        # 连接OBS
        client = obs.ReqClient(host="localhost", port=4455, password=None, timeout=3)
        print("✅ 成功连接到OBS")
        
        # 检查媒体源是否存在
        print(f"\n📋 检查音频源:")
        try:
            scene_list = client.get_scene_list()
            current_scene = scene_list.current_program_scene_name
            print(f"当前场景: {current_scene}")
            
            scene_items = client.get_scene_item_list(current_scene)
            
            media_source_found = False
            for item in scene_items.scene_items:
                source_name = item['sourceName']
                if source_name == "媒体源":
                    media_source_found = True
                    print(f"✅ 找到'媒体源'")
                    break
            
            if not media_source_found:
                print("❌ 未找到'媒体源'")
                print("💡 请确认OBS中是否有名为'媒体源'的音频源")
                return
                
        except Exception as e:
            print(f"⚠️ 检查音频源时出错: {e}")
        
        # 检查VST滤镜
        print(f"\n🎛️ 检查VST滤镜:")
        try:
            filter_list = client.get_source_filter_list("媒体源")
            filters = filter_list.filters
            
            if not filters:
                print("❌ '媒体源'没有任何滤镜")
                print("💡 请为'媒体源'添加'VST 2.x 插件'滤镜")
                return
            
            print(f"发现 {len(filters)} 个滤镜:")
            
            vst_found = False
            for filter_info in filters:
                filter_name = filter_info['filterName']
                filter_enabled = filter_info['filterEnabled']
                status = "✅ 启用" if filter_enabled else "❌ 禁用"
                
                print(f"  - {filter_name}: {status}")
                
                if "VST" in filter_name or "vst" in filter_name:
                    vst_found = True
                    
                    if filter_enabled:
                        # 检查这个VST滤镜的数据
                        try:
                            filter_detail = client.get_source_filter("媒体源", filter_name)
                            settings = filter_detail.filter_settings
                            
                            chunk_data = settings.get('chunk_data', '')
                            chunk_len = len(chunk_data) if chunk_data else 0
                            
                            print(f"    📊 chunk_data: {chunk_len} 字符")
                            
                            if chunk_len == 0:
                                print(f"    ❌ chunk_data为空 - Graillon插件未加载数据")
                                print(f"    💡 解决方法:")
                                print(f"       1. 双击'{filter_name}'打开Graillon界面")
                                print(f"       2. 调整任意参数(如音调)")
                                print(f"       3. 关闭Graillon界面")
                                print(f"       4. 重新运行此诊断")
                            else:
                                print(f"    ✅ chunk_data有数据 - 插件已加载!")
                                print(f"    🎯 可以尝试控制参数了")
                                
                        except Exception as e:
                            print(f"    ⚠️ 无法获取滤镜详情: {e}")
            
            if not vst_found:
                print("❌ 未找到VST滤镜")
                print("💡 请添加'VST 2.x 插件'滤镜并选择Graillon.dll")
                
        except Exception as e:
            print(f"❌ 检查滤镜时出错: {e}")
            print("💡 可能的原因:")
            print("   - '媒体源'不存在")
            print("   - OBS版本不兼容")
        
        # 给出下一步建议
        print(f"\n📝 下一步操作:")
        print("1. 确保Graillon插件界面能正常打开")
        print("2. 在Graillon中调整参数后关闭界面")
        print("3. 运行 '简单测试.py' 查看chunk数据")
        print("4. 如果有数据，可以尝试参数控制")
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("\n🔧 请检查:")
        print("1. OBS是否运行")
        print("2. WebSocket服务器是否启用 (工具→WebSocket服务器设置)")
        print("3. 端口设置为4455")

if __name__ == "__main__":
    main()
