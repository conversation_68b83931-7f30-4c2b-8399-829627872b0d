#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Graillon最终控制器
基于实际测试结果，通过chunk_data控制Graillon插件
"""

import obsws_python as obs
import base64
import struct
import time
import random
import sys

class GraillonChunkController:
    def __init__(self, host="localhost", port=4455, password=None):
        """初始化OBS WebSocket连接"""
        try:
            self.client = obs.ReqClient(host=host, port=port, password=password, timeout=3)
            print("✅ 成功连接到OBS WebSocket")
        except Exception as e:
            print(f"❌ 连接OBS失败: {e}")
            sys.exit(1)
        
        self.source_name = "媒体源"
        self.filter_name = "VST 2.x 插件"
        self.baseline_chunk = None
        self.baseline_hash = None
    
    def get_current_chunk(self):
        """获取当前的chunk_data"""
        try:
            response = self.client.get_source_filter(
                source_name=self.source_name,
                filter_name=self.filter_name
            )
            
            settings = response.filter_settings
            self.baseline_chunk = settings.get('chunk_data', '')
            self.baseline_hash = settings.get('chunk_hash', '')
            
            print(f"📊 获取到chunk数据:")
            print(f"   chunk_data长度: {len(self.baseline_chunk)}")
            print(f"   chunk_hash: {self.baseline_hash}")
            
            if self.baseline_chunk:
                # 尝试解析chunk数据
                try:
                    decoded = base64.b64decode(self.baseline_chunk)
                    print(f"   解码后长度: {len(decoded)} 字节")
                    
                    # 显示前32字节的十六进制
                    hex_preview = ' '.join(f'{b:02x}' for b in decoded[:32])
                    print(f"   数据预览: {hex_preview}...")
                    
                    return True
                except Exception as e:
                    print(f"   解码失败: {e}")
                    return False
            else:
                print("   ⚠️ chunk_data为空")
                return False
                
        except Exception as e:
            print(f"❌ 获取chunk数据失败: {e}")
            return False
    
    def modify_chunk_float(self, offset, new_value):
        """在指定偏移位置修改float值"""
        if not self.baseline_chunk:
            print("❌ 没有基准chunk数据")
            return None
        
        try:
            # 解码base64数据
            decoded_data = bytearray(base64.b64decode(self.baseline_chunk))
            
            # 检查偏移量是否有效
            if offset + 4 > len(decoded_data):
                print(f"⚠️ 偏移量 {offset} 超出数据范围 ({len(decoded_data)} 字节)")
                return None
            
            # 将float值写入指定位置（小端序）
            struct.pack_into('<f', decoded_data, offset, float(new_value))
            
            # 重新编码为base64
            new_chunk_data = base64.b64encode(decoded_data).decode('utf-8')
            
            print(f"🔧 修改偏移 {offset}: {new_value}")
            return new_chunk_data
            
        except Exception as e:
            print(f"❌ 修改chunk数据失败: {e}")
            return None
    
    def apply_chunk_data(self, new_chunk_data):
        """应用新的chunk数据"""
        try:
            self.client.set_source_filter_settings(
                source_name=self.source_name,
                filter_name=self.filter_name,
                filter_settings={'chunk_data': new_chunk_data}
            )
            return True
        except Exception as e:
            print(f"❌ 应用chunk数据失败: {e}")
            return False
    
    def test_chunk_offsets(self):
        """测试不同偏移位置的效果"""
        print("🧪 开始测试chunk偏移位置")
        print("=" * 50)
        
        if not self.get_current_chunk():
            return
        
        # 常见的VST参数偏移位置（基于经验）
        test_offsets = [
            8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64
        ]
        
        original_chunk = self.baseline_chunk
        
        for offset in test_offsets:
            print(f"\n🎯 测试偏移 {offset}:")
            
            # 测试不同的值
            test_values = [0.0, 3.0, 6.0, -3.0]
            
            for value in test_values:
                new_chunk = self.modify_chunk_float(offset, value)
                if new_chunk:
                    success = self.apply_chunk_data(new_chunk)
                    if success:
                        print(f"  ✅ 偏移{offset} = {value}: 设置成功")
                        time.sleep(0.5)  # 短暂等待观察效果
                    else:
                        print(f"  ❌ 偏移{offset} = {value}: 设置失败")
                        break
                else:
                    print(f"  ❌ 偏移{offset}: 无法修改")
                    break
            
            # 恢复原始数据
            self.apply_chunk_data(original_chunk)
            time.sleep(0.2)
        
        print(f"\n🎉 偏移测试完成")
    
    def smart_parameter_search(self):
        """智能参数搜索"""
        print("🔍 开始智能参数搜索")
        print("=" * 50)
        
        if not self.get_current_chunk():
            return
        
        try:
            decoded = base64.b64decode(self.baseline_chunk)
            data_length = len(decoded)
            
            print(f"📊 数据分析:")
            print(f"   总长度: {data_length} 字节")
            print(f"   可能的float参数位置: {data_length // 4} 个")
            
            # 分析每4字节作为float值
            print(f"\n🔢 当前float值分析:")
            for i in range(0, min(data_length, 64), 4):  # 只分析前64字节
                if i + 4 <= data_length:
                    float_val = struct.unpack('<f', decoded[i:i+4])[0]
                    print(f"   偏移{i:2d}: {float_val:8.3f}")
            
            # 寻找可能的音调参数（通常在-24到24之间）
            print(f"\n🎵 可能的音调参数位置:")
            potential_pitch_offsets = []
            
            for i in range(0, data_length - 4, 4):
                float_val = struct.unpack('<f', decoded[i:i+4])[0]
                if -24.0 <= float_val <= 24.0 and abs(float_val) < 20:
                    potential_pitch_offsets.append((i, float_val))
                    print(f"   偏移{i:2d}: {float_val:6.1f} ← 可能是音调参数")
            
            return potential_pitch_offsets
            
        except Exception as e:
            print(f"❌ 数据分析失败: {e}")
            return []
    
    def test_potential_pitch_params(self, potential_offsets):
        """测试潜在的音调参数"""
        print(f"\n🎯 测试潜在音调参数 (共{len(potential_offsets)}个)")
        print("=" * 50)
        
        original_chunk = self.baseline_chunk
        
        for offset, original_value in potential_offsets:
            print(f"\n🎵 测试偏移 {offset} (原值: {original_value:.1f}):")
            
            # 测试音调变化
            test_pitches = [original_value + 3, original_value + 6, original_value - 3]
            
            for pitch in test_pitches:
                # 确保在合理范围内
                pitch = max(-12.0, min(12.0, pitch))
                
                new_chunk = self.modify_chunk_float(offset, pitch)
                if new_chunk and self.apply_chunk_data(new_chunk):
                    print(f"  🎶 音调 {pitch:+.1f}: 设置成功 - 请听音频变化")
                    time.sleep(2)  # 等待足够时间听到变化
                else:
                    print(f"  ❌ 音调 {pitch:+.1f}: 设置失败")
            
            # 恢复原始值
            self.apply_chunk_data(original_chunk)
            print(f"  🔄 已恢复原始值")
            time.sleep(1)
    
    def interactive_control(self):
        """交互式控制"""
        print("🎛️ 交互式Graillon控制")
        print("=" * 40)
        
        if not self.get_current_chunk():
            return
        
        # 先进行智能搜索
        potential_offsets = self.smart_parameter_search()
        
        if potential_offsets:
            print(f"\n🎯 发现 {len(potential_offsets)} 个潜在参数位置")
            
            # 让用户选择要测试的偏移
            print("\n请选择要控制的参数:")
            for i, (offset, value) in enumerate(potential_offsets, 1):
                print(f"[{i}] 偏移 {offset} (当前值: {value:.1f})")
            
            try:
                choice = int(input("请选择 (1-{}): ".format(len(potential_offsets)))) - 1
                if 0 <= choice < len(potential_offsets):
                    selected_offset, original_value = potential_offsets[choice]
                    
                    print(f"\n🎵 控制偏移 {selected_offset}:")
                    print("输入新的音调值 (-12 到 12，输入 'q' 退出):")
                    
                    while True:
                        try:
                            user_input = input("音调值: ").strip()
                            if user_input.lower() == 'q':
                                break
                            
                            pitch_value = float(user_input)
                            pitch_value = max(-12.0, min(12.0, pitch_value))
                            
                            new_chunk = self.modify_chunk_float(selected_offset, pitch_value)
                            if new_chunk and self.apply_chunk_data(new_chunk):
                                print(f"✅ 音调设置为 {pitch_value:+.1f}")
                            else:
                                print("❌ 设置失败")
                                
                        except ValueError:
                            print("请输入有效的数字")
                        except KeyboardInterrupt:
                            break
                    
                    # 恢复原始值
                    self.apply_chunk_data(self.baseline_chunk)
                    print("🔄 已恢复原始设置")
                    
            except (ValueError, KeyboardInterrupt):
                print("操作取消")
        else:
            print("❌ 未发现潜在的参数位置")

def main():
    """主函数"""
    print("🎛️ Graillon最终控制器")
    print("基于实际测试结果的chunk_data控制方案")
    print("=" * 60)
    
    controller = GraillonChunkController()
    
    print("\n📋 选择操作模式:")
    print("1. 智能参数搜索")
    print("2. 测试chunk偏移")
    print("3. 交互式控制")
    
    try:
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            controller.smart_parameter_search()
        elif choice == "2":
            controller.test_chunk_offsets()
        elif choice == "3":
            controller.interactive_control()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    except Exception as e:
        print(f"❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
