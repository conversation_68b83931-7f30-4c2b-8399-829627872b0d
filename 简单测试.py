#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Graillon测试 - 直接显示chunk数据分析结果
"""

import obsws_python as obs
import base64
import struct
import sys

def main():
    print("🎛️ Graillon Chunk数据分析")
    print("=" * 40)
    
    try:
        # 连接OBS
        client = obs.ReqClient(host="localhost", port=4455, password=None, timeout=3)
        print("✅ 成功连接到OBS")
        
        # 获取滤镜数据
        response = client.get_source_filter(
            source_name="媒体源",
            filter_name="VST 2.x 插件"
        )
        
        settings = response.filter_settings
        chunk_data = settings.get('chunk_data', '')
        chunk_hash = settings.get('chunk_hash', '')
        
        print(f"\n📊 Graillon数据分析:")
        print(f"   chunk_data长度: {len(chunk_data)}")
        print(f"   chunk_hash: {chunk_hash}")
        
        if chunk_data:
            # 解码并分析
            decoded = base64.b64decode(chunk_data)
            print(f"   解码后长度: {len(decoded)} 字节")
            
            # 显示十六进制数据
            print(f"\n🔢 前64字节十六进制:")
            for i in range(0, min(64, len(decoded)), 16):
                hex_line = ' '.join(f'{b:02x}' for b in decoded[i:i+16])
                print(f"   {i:04x}: {hex_line}")
            
            # 分析float值
            print(f"\n🎵 可能的参数值 (float解析):")
            for i in range(0, min(len(decoded), 128), 4):
                if i + 4 <= len(decoded):
                    try:
                        float_val = struct.unpack('<f', decoded[i:i+4])[0]
                        # 只显示合理范围内的值
                        if -100 <= float_val <= 100 and not (float_val == 0.0):
                            print(f"   偏移{i:2d}: {float_val:8.3f}")
                    except:
                        pass
            
            # 寻找可能的音调参数
            print(f"\n🎯 可能的音调参数位置:")
            found_params = False
            for i in range(0, len(decoded) - 4, 4):
                try:
                    float_val = struct.unpack('<f', decoded[i:i+4])[0]
                    # 音调参数通常在-24到24之间，且不为0
                    if -24.0 <= float_val <= 24.0 and abs(float_val) > 0.1:
                        print(f"   偏移{i:2d}: {float_val:6.1f} ← 可能是音调!")
                        found_params = True
                except:
                    pass
            
            if not found_params:
                print("   未发现明显的音调参数")
            
            print(f"\n💡 建议:")
            print(f"   1. 在Graillon界面中调整音调参数")
            print(f"   2. 重新运行此脚本查看数值变化")
            print(f"   3. 对比前后数据找出音调参数位置")
        else:
            print("   ⚠️ chunk_data为空，可能插件未加载")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("\n🔧 请检查:")
        print("1. OBS是否运行")
        print("2. WebSocket服务器是否启用")
        print("3. '媒体源' 和 'VST 2.x 插件' 名称是否正确")

if __name__ == "__main__":
    main()
