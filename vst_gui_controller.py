#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VST控制器GUI界面
提供图形化界面来控制OBS中的VST插件
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from vst_complete_controller import VSTCompleteController

class VSTControllerGUI:
    def __init__(self):
        self.controller = VSTCompleteController()
        self.root = tk.Tk()
        self.root.title("VST控制器 - OBS音频效果控制")
        self.root.geometry("800x600")
        
        # 状态变量
        self.is_connected = False
        self.selected_source = None
        self.current_filters = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="连接设置", padding="5")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.toggle_connection)
        self.connect_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="未连接", foreground="red")
        self.status_label.grid(row=0, column=1)
        
        # 音频源选择
        source_frame = ttk.LabelFrame(main_frame, text="音频源", padding="5")
        source_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(source_frame, text="选择音频源:").grid(row=0, column=0, sticky=tk.W)
        self.source_combo = ttk.Combobox(source_frame, state="readonly", width=30)
        self.source_combo.grid(row=0, column=1, padx=(5, 10), sticky=(tk.W, tk.E))
        self.source_combo.bind('<<ComboboxSelected>>', self.on_source_selected)
        
        ttk.Button(source_frame, text="刷新", command=self.refresh_sources).grid(row=0, column=2)
        
        # VST插件管理
        plugin_frame = ttk.LabelFrame(main_frame, text="VST插件管理", padding="5")
        plugin_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 插件选择
        ttk.Label(plugin_frame, text="插件类型:").grid(row=0, column=0, sticky=tk.W)
        self.plugin_combo = ttk.Combobox(plugin_frame, values=["Graillon", "TSE808", "TAL-Reverb"], 
                                        state="readonly", width=15)
        self.plugin_combo.grid(row=0, column=1, padx=(5, 10))
        self.plugin_combo.set("Graillon")
        
        ttk.Label(plugin_frame, text="滤镜名称:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.filter_name_entry = ttk.Entry(plugin_frame, width=15)
        self.filter_name_entry.grid(row=0, column=3, padx=(5, 10))
        self.filter_name_entry.insert(0, "Graillon变声")
        
        ttk.Button(plugin_frame, text="添加滤镜", command=self.add_filter).grid(row=0, column=4)
        
        # 当前滤镜列表
        filter_list_frame = ttk.LabelFrame(main_frame, text="当前滤镜", padding="5")
        filter_list_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 滤镜树形控件
        self.filter_tree = ttk.Treeview(filter_list_frame, columns=("status",), height=6)
        self.filter_tree.heading("#0", text="滤镜名称")
        self.filter_tree.heading("status", text="状态")
        self.filter_tree.column("#0", width=200)
        self.filter_tree.column("status", width=80)
        self.filter_tree.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滤镜操作按钮
        ttk.Button(filter_list_frame, text="启用", command=self.enable_filter).grid(row=1, column=0, pady=(5, 0))
        ttk.Button(filter_list_frame, text="禁用", command=self.disable_filter).grid(row=1, column=1, pady=(5, 0))
        ttk.Button(filter_list_frame, text="删除", command=self.remove_filter).grid(row=1, column=2, pady=(5, 0))
        
        # 参数控制区域
        param_frame = ttk.LabelFrame(main_frame, text="参数控制", padding="5")
        param_frame.grid(row=3, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10), padx=(10, 0))
        
        # 参数输入
        ttk.Label(param_frame, text="参数名:").grid(row=0, column=0, sticky=tk.W)
        self.param_name_entry = ttk.Entry(param_frame, width=15)
        self.param_name_entry.grid(row=0, column=1, padx=(5, 0))
        
        ttk.Label(param_frame, text="值:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.param_value_entry = ttk.Entry(param_frame, width=15)
        self.param_value_entry.grid(row=1, column=1, padx=(5, 0), pady=(5, 0))
        
        ttk.Button(param_frame, text="设置参数", command=self.set_parameter).grid(row=2, column=0, columnspan=2, pady=(10, 0))
        
        # 快速参数控制
        quick_frame = ttk.LabelFrame(param_frame, text="快速控制", padding="5")
        quick_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Graillon快速控制
        ttk.Label(quick_frame, text="音调:").grid(row=0, column=0, sticky=tk.W)
        self.pitch_scale = ttk.Scale(quick_frame, from_=-12, to=12, orient=tk.HORIZONTAL, 
                                    command=self.on_pitch_change)
        self.pitch_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0))
        self.pitch_scale.set(0)
        
        ttk.Label(quick_frame, text="混合:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.mix_scale = ttk.Scale(quick_frame, from_=0, to=100, orient=tk.HORIZONTAL,
                                  command=self.on_mix_change)
        self.mix_scale.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(5, 0))
        self.mix_scale.set(100)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        filter_list_frame.columnconfigure(0, weight=1)
        param_frame.columnconfigure(1, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        quick_frame.columnconfigure(1, weight=1)
        
    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def toggle_connection(self):
        """切换连接状态"""
        if not self.is_connected:
            self.log("正在连接到OBS...")
            if self.controller.connect():
                self.is_connected = True
                self.connect_btn.config(text="断开连接")
                self.status_label.config(text="已连接", foreground="green")
                self.log("✅ 成功连接到OBS")
                self.refresh_sources()
            else:
                self.log("❌ 连接失败")
        else:
            self.controller.disconnect()
            self.is_connected = False
            self.connect_btn.config(text="连接OBS")
            self.status_label.config(text="未连接", foreground="red")
            self.log("已断开连接")
            
    def refresh_sources(self):
        """刷新音频源列表"""
        if not self.is_connected:
            return
            
        self.log("刷新音频源列表...")
        sources = self.controller.get_audio_sources()
        self.source_combo['values'] = sources
        
        if sources:
            self.source_combo.set(sources[0])
            self.selected_source = sources[0]
            self.log(f"找到 {len(sources)} 个音频源")
            self.refresh_filters()
        else:
            self.log("未找到音频源")
            
    def on_source_selected(self, event):
        """音频源选择事件"""
        self.selected_source = self.source_combo.get()
        self.log(f"选择音频源: {self.selected_source}")
        self.refresh_filters()
        
    def refresh_filters(self):
        """刷新滤镜列表"""
        if not self.selected_source:
            return
            
        # 清空树形控件
        for item in self.filter_tree.get_children():
            self.filter_tree.delete(item)
            
        # 获取VST滤镜
        filters = self.controller.list_vst_filters(self.selected_source)
        self.current_filters = {}
        
        for filter_info in filters:
            name = filter_info['name']
            status = "✅ 启用" if filter_info['enabled'] else "❌ 禁用"
            
            item_id = self.filter_tree.insert('', tk.END, text=name, values=(status,))
            self.current_filters[name] = filter_info
            
        self.log(f"找到 {len(filters)} 个VST滤镜")
        
    def add_filter(self):
        """添加VST滤镜"""
        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择音频源")
            return
            
        plugin_name = self.plugin_combo.get()
        filter_name = self.filter_name_entry.get().strip()
        
        if not plugin_name or not filter_name:
            messagebox.showwarning("警告", "请输入插件类型和滤镜名称")
            return
            
        self.log(f"添加VST滤镜: {filter_name} ({plugin_name})")
        
        def add_thread():
            success = self.controller.add_vst_filter(self.selected_source, filter_name, plugin_name)
            if success:
                self.root.after(0, lambda: [
                    self.log(f"✅ 滤镜添加成功: {filter_name}"),
                    self.refresh_filters()
                ])
            else:
                self.root.after(0, lambda: self.log(f"❌ 滤镜添加失败: {filter_name}"))
        
        threading.Thread(target=add_thread, daemon=True).start()
        
    def get_selected_filter(self):
        """获取选中的滤镜"""
        selection = self.filter_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个滤镜")
            return None
        return self.filter_tree.item(selection[0])['text']
        
    def enable_filter(self):
        """启用滤镜"""
        filter_name = self.get_selected_filter()
        if filter_name:
            success = self.controller.enable_vst_filter(self.selected_source, filter_name, True)
            if success:
                self.log(f"✅ 滤镜已启用: {filter_name}")
                self.refresh_filters()
                
    def disable_filter(self):
        """禁用滤镜"""
        filter_name = self.get_selected_filter()
        if filter_name:
            success = self.controller.enable_vst_filter(self.selected_source, filter_name, False)
            if success:
                self.log(f"✅ 滤镜已禁用: {filter_name}")
                self.refresh_filters()
                
    def remove_filter(self):
        """删除滤镜"""
        filter_name = self.get_selected_filter()
        if filter_name:
            if messagebox.askyesno("确认", f"确定要删除滤镜 '{filter_name}' 吗？"):
                success = self.controller.remove_vst_filter(self.selected_source, filter_name)
                if success:
                    self.log(f"✅ 滤镜已删除: {filter_name}")
                    self.refresh_filters()
                    
    def set_parameter(self):
        """设置参数"""
        filter_name = self.get_selected_filter()
        if not filter_name:
            return
            
        param_name = self.param_name_entry.get().strip()
        param_value_str = self.param_value_entry.get().strip()
        
        if not param_name or not param_value_str:
            messagebox.showwarning("警告", "请输入参数名和值")
            return
            
        try:
            param_value = float(param_value_str)
        except ValueError:
            messagebox.showerror("错误", "参数值必须是数字")
            return
            
        success = self.controller.set_vst_parameter_by_name(
            self.selected_source, filter_name, param_name, param_value
        )
        
        if success:
            self.log(f"✅ 参数设置成功: {param_name} = {param_value}")
        else:
            self.log(f"❌ 参数设置失败: {param_name}")
            
    def on_pitch_change(self, value):
        """音调滑块变化"""
        filter_name = self.get_selected_filter()
        if filter_name and "Graillon" in filter_name:
            pitch_value = float(value)
            self.controller.set_vst_parameter_by_name(
                self.selected_source, filter_name, "pitch", pitch_value
            )
            
    def on_mix_change(self, value):
        """混合滑块变化"""
        filter_name = self.get_selected_filter()
        if filter_name and "Graillon" in filter_name:
            mix_value = float(value)
            self.controller.set_vst_parameter_by_name(
                self.selected_source, filter_name, "mix", mix_value
            )
            
    def run(self):
        """运行GUI"""
        self.log("🎛️ VST控制器已启动")
        self.log("请先连接到OBS，然后选择音频源")
        
        try:
            self.root.mainloop()
        finally:
            if self.is_connected:
                self.controller.disconnect()

if __name__ == "__main__":
    app = VSTControllerGUI()
    app.run()
