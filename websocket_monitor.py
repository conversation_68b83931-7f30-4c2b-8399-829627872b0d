#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket通信监听工具
用于捕获和分析OBS WebSocket通信内容
"""

import asyncio
import websockets
import json
import time
from datetime import datetime
import threading
import tkinter as tk
from tkinter import scrolledtext, ttk

class WebSocketMonitor:
    def __init__(self):
        """初始化WebSocket监听器"""
        self.root = tk.Tk()
        self.root.title("OBS WebSocket 通信监听器")
        self.root.geometry("1000x700")
        
        self.is_monitoring = False
        self.server = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 控制区域
        control_frame = ttk.Frame(self.root, padding="10")
        control_frame.pack(fill=tk.X)
        
        # 监听端口设置
        ttk.Label(control_frame, text="代理端口:").pack(side=tk.LEFT)
        self.port_var = tk.StringVar(value="4456")
        ttk.Entry(control_frame, textvariable=self.port_var, width=10).pack(side=tk.LEFT, padx=(5, 10))

        ttk.Label(control_frame, text="OBS端口:").pack(side=tk.LEFT, padx=(10, 0))
        self.obs_port_var = tk.StringVar(value="4455")
        ttk.Entry(control_frame, textvariable=self.obs_port_var, width=10).pack(side=tk.LEFT, padx=(5, 10))
        
        # 控制按钮
        self.start_btn = ttk.Button(control_frame, text="开始监听", command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="停止监听", command=self.stop_monitoring, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态显示
        self.status_label = ttk.Label(control_frame, text="未监听", foreground="red")
        self.status_label.pack(side=tk.RIGHT)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(self.root, text="WebSocket通信日志", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 说明信息
        info_frame = ttk.LabelFrame(self.root, text="使用说明", padding="5")
        info_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        info_text = """
监听方案：
方案1 - 直接监听（推荐）：
  1. 在OBS中踢出现有WebSocket连接
  2. 设置代理端口为4455，OBS端口留空
  3. 启动监听，重新运行目标程序

方案2 - 代理转发：
  1. 设置代理端口4456，OBS端口4455
  2. 修改目标程序连接地址为 ws://localhost:4456
  3. 启动监听，程序会自动转发并记录消息

当前OBS有活跃连接，建议使用方案1
        """
        ttk.Label(info_frame, text=info_text.strip(), justify=tk.LEFT).pack(anchor=tk.W)
        
    def log(self, message, msg_type="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        # 根据消息类型设置颜色标记
        if msg_type == "SEND":
            prefix = "📤 发送"
            color = "blue"
        elif msg_type == "RECV":
            prefix = "📥 接收"
            color = "green"
        elif msg_type == "ERROR":
            prefix = "❌ 错误"
            color = "red"
        elif msg_type == "CONN":
            prefix = "🔗 连接"
            color = "purple"
        else:
            prefix = "ℹ️ 信息"
            color = "black"
            
        log_message = f"[{timestamp}] {prefix}: {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("日志已清空")
        
    def save_log(self):
        """保存日志到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"websocket_log_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
            self.log(f"日志已保存到: {filename}")
        except Exception as e:
            self.log(f"保存日志失败: {e}", "ERROR")
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        client_addr = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        self.log(f"客户端连接: {client_addr}", "CONN")
        
        # 检查是否为直接监听模式
        obs_port = self.obs_port_var.get().strip()
        if not obs_port:
            # 直接监听模式 - 不转发，只记录
            self.log(f"直接监听模式 - 记录来自 {client_addr} 的消息", "CONN")
            try:
                async for message in websocket:
                    try:
                        json_data = json.loads(message)
                        formatted_msg = json.dumps(json_data, indent=2, ensure_ascii=False)
                        self.log(f"收到消息:\n{formatted_msg}", "RECV")
                    except json.JSONDecodeError:
                        self.log(f"收到消息: {message}", "RECV")
            except websockets.exceptions.ConnectionClosed:
                pass
            except Exception as e:
                self.log(f"监听消息时出错: {e}", "ERROR")
            return

        # 代理转发模式 - 连接到真正的OBS WebSocket服务器
        try:
            async with websockets.connect(f"ws://localhost:{obs_port}") as obs_websocket:
                self.log("已连接到OBS WebSocket服务器", "CONN")
                
                # 创建双向转发任务
                client_to_obs = asyncio.create_task(
                    self.forward_messages(websocket, obs_websocket, "客户端→OBS", "SEND")
                )
                obs_to_client = asyncio.create_task(
                    self.forward_messages(obs_websocket, websocket, "OBS→客户端", "RECV")
                )
                
                # 等待任一任务完成（连接断开）
                done, pending = await asyncio.wait(
                    [client_to_obs, obs_to_client],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # 取消未完成的任务
                for task in pending:
                    task.cancel()
                    
        except Exception as e:
            self.log(f"连接OBS失败: {e}", "ERROR")
        finally:
            self.log(f"客户端断开: {client_addr}", "CONN")
    
    async def forward_messages(self, source, target, direction, msg_type):
        """转发消息并记录"""
        try:
            async for message in source:
                # 记录消息内容
                try:
                    # 尝试解析JSON并格式化
                    json_data = json.loads(message)
                    formatted_msg = json.dumps(json_data, indent=2, ensure_ascii=False)
                    self.log(f"{direction}:\n{formatted_msg}", msg_type)
                except json.JSONDecodeError:
                    # 如果不是JSON，直接记录
                    self.log(f"{direction}: {message}", msg_type)
                
                # 转发消息
                await target.send(message)
                
        except websockets.exceptions.ConnectionClosed:
            pass
        except Exception as e:
            self.log(f"转发消息时出错: {e}", "ERROR")
    
    def start_monitoring(self):
        """开始监听"""
        if self.is_monitoring:
            return
            
        try:
            port = int(self.port_var.get())
        except ValueError:
            self.log("端口号必须是数字", "ERROR")
            return
        
        self.is_monitoring = True
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.status_label.config(text=f"监听中 (端口{port})", foreground="green")
        
        # 在新线程中启动异步服务器
        def run_server():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 创建服务器
                start_server = websockets.serve(self.handle_client, "localhost", port)
                self.server = loop.run_until_complete(start_server)

                self.root.after(0, lambda: self.log(f"WebSocket代理服务器已启动，监听端口: {port}"))
                self.root.after(0, lambda: self.log("请将目标程序的连接地址改为: ws://localhost:" + str(port)))

                # 运行事件循环
                loop.run_forever()
            except Exception as e:
                self.root.after(0, lambda: self.log(f"启动服务器失败: {e}", "ERROR"))
                self.root.after(0, self.stop_monitoring)
            finally:
                loop.close()
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
    
    def stop_monitoring(self):
        """停止监听"""
        if not self.is_monitoring:
            return
            
        self.is_monitoring = False
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.status_label.config(text="未监听", foreground="red")
        
        if self.server:
            self.server.close()
            
        self.log("监听已停止")
    
    def run(self):
        """运行程序"""
        self.log("🚀 WebSocket监听器已启动")
        self.log("💡 使用说明:")
        self.log("   1. 点击'开始监听'启动代理服务器")
        self.log("   2. 修改目标程序连接到代理端口")
        self.log("   3. 查看捕获的WebSocket通信内容")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log("程序被用户中断")
        finally:
            self.stop_monitoring()

if __name__ == "__main__":
    monitor = WebSocketMonitor()
    monitor.run()
