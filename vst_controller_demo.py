#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VST控制器演示程序
展示如何使用VSTCompleteController来控制OBS中的VST插件
"""

import time
import sys
from vst_complete_controller import VSTCompleteController

def main():
    """主演示程序"""
    print("🎛️ VST控制器演示程序")
    print("=" * 50)
    
    # 创建控制器实例
    controller = VSTCompleteController()
    
    # 连接到OBS
    if not controller.connect():
        print("❌ 无法连接到OBS，程序退出")
        return
    
    try:
        # 1. 获取音频源列表
        print("\n📋 获取音频源列表...")
        audio_sources = controller.get_audio_sources()
        
        if not audio_sources:
            print("❌ 未找到音频源")
            return
        
        print("🎵 可用的音频源:")
        for i, source in enumerate(audio_sources):
            print(f"   {i+1}. {source}")
        
        # 选择音频源（这里使用第一个，实际使用时可以让用户选择）
        if len(audio_sources) > 0:
            selected_source = audio_sources[0]
            print(f"\n✅ 选择音频源: {selected_source}")
        else:
            print("❌ 没有可用的音频源")
            return
        
        # 2. 添加VST滤镜
        print("\n🔧 添加VST滤镜...")
        
        # 添加Graillon插件
        if controller.add_vst_filter(selected_source, "Graillon变声", "Graillon"):
            print("✅ Graillon插件添加成功")
            
            # 等待插件完全加载
            time.sleep(3)
            
            # 3. 获取当前参数
            print("\n📊 获取当前参数...")
            params = controller.get_vst_parameters(selected_source, "Graillon变声")
            print("当前参数:")
            for name, value in params.items():
                print(f"   {name}: {value}")
            
            # 4. 测试参数控制
            print("\n🎛️ 测试参数控制...")
            
            # 方法1: 通过参数名设置
            print("方法1: 通过参数名设置...")
            test_params = [
                ("pitch", 5.0),    # 音调+5半音
                ("formant", 120.0), # 共振峰调整
                ("mix", 80.0)       # 混合比例80%
            ]
            
            for param_name, value in test_params:
                success = controller.set_vst_parameter_by_name(
                    selected_source, "Graillon变声", param_name, value
                )
                if success:
                    print(f"   ✅ {param_name} = {value}")
                    time.sleep(1)  # 等待参数生效
                else:
                    print(f"   ❌ {param_name} 设置失败")
            
            # 方法2: 通过chunk数据设置
            print("\n方法2: 通过chunk数据设置...")
            chunk_tests = [
                (0, 0.5),   # 参数0设为0.5
                (1, 0.8),   # 参数1设为0.8
                (2, 0.3)    # 参数2设为0.3
            ]
            
            for param_index, value in chunk_tests:
                success = controller.set_vst_parameter_by_chunk(
                    selected_source, "Graillon变声", param_index, value
                )
                if success:
                    print(f"   ✅ 参数{param_index} = {value}")
                    time.sleep(1)
                else:
                    print(f"   ❌ 参数{param_index} 设置失败")
            
            # 5. 演示实时参数变化
            print("\n🎵 演示实时参数变化 (10秒)...")
            start_time = time.time()
            
            while time.time() - start_time < 10:
                # 创建一个正弦波形的音调变化
                elapsed = time.time() - start_time
                pitch_value = 3.0 * (1 + 0.5 * (elapsed % 2 - 1))  # 在1.5到4.5之间变化
                
                controller.set_vst_parameter_by_name(
                    selected_source, "Graillon变声", "pitch", pitch_value
                )
                
                print(f"   🎵 音调: {pitch_value:.2f} 半音")
                time.sleep(0.5)
            
            # 6. 重置参数
            print("\n🔄 重置参数...")
            reset_params = [
                ("pitch", 0.0),
                ("formant", 100.0),
                ("mix", 100.0)
            ]
            
            for param_name, value in reset_params:
                controller.set_vst_parameter_by_name(
                    selected_source, "Graillon变声", param_name, value
                )
                print(f"   ✅ {param_name} 重置为 {value}")
        
        # 7. 添加其他插件演示
        print("\n🔧 添加其他VST插件...")
        
        # 添加TSE808
        if controller.add_vst_filter(selected_source, "TSE808失真", "TSE808"):
            print("✅ TSE808插件添加成功")
            
            # 测试TSE808参数
            controller.set_vst_parameter_by_name(selected_source, "TSE808失真", "drive", 70.0)
            controller.set_vst_parameter_by_name(selected_source, "TSE808失真", "tone", 60.0)
            time.sleep(2)
        
        # 添加TAL-Reverb
        if controller.add_vst_filter(selected_source, "TAL混响", "TAL-Reverb"):
            print("✅ TAL-Reverb插件添加成功")
            
            # 测试混响参数
            controller.set_vst_parameter_by_name(selected_source, "TAL混响", "roomsize", 70.0)
            controller.set_vst_parameter_by_name(selected_source, "TAL混响", "wet", 40.0)
            time.sleep(2)
        
        # 8. 列出所有VST滤镜
        print("\n📋 当前VST滤镜列表:")
        vst_filters = controller.list_vst_filters(selected_source)
        for filter_info in vst_filters:
            status = "✅ 启用" if filter_info['enabled'] else "❌ 禁用"
            print(f"   🎛️ {filter_info['name']} ({filter_info['kind']}) - {status}")
        
        print("\n🎉 演示完成！")
        print("💡 提示: 你现在可以在OBS中听到音频效果的变化")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
    finally:
        # 清理资源
        controller.disconnect()
        print("👋 程序结束")

def interactive_mode():
    """交互模式 - 让用户手动控制参数"""
    print("🎛️ VST控制器交互模式")
    print("=" * 50)
    
    controller = VSTCompleteController()
    
    if not controller.connect():
        print("❌ 无法连接到OBS")
        return
    
    try:
        # 获取音频源
        audio_sources = controller.get_audio_sources()
        if not audio_sources:
            print("❌ 未找到音频源")
            return
        
        print("🎵 选择音频源:")
        for i, source in enumerate(audio_sources):
            print(f"   {i+1}. {source}")
        
        try:
            choice = int(input("请输入序号: ")) - 1
            if 0 <= choice < len(audio_sources):
                selected_source = audio_sources[choice]
                print(f"✅ 选择了: {selected_source}")
            else:
                print("❌ 无效选择")
                return
        except ValueError:
            print("❌ 请输入数字")
            return
        
        # 交互式控制循环
        print("\n🎛️ 交互式控制模式")
        print("命令:")
        print("  add <插件名> <滤镜名> - 添加VST滤镜")
        print("  set <滤镜名> <参数名> <值> - 设置参数")
        print("  get <滤镜名> - 获取参数")
        print("  list - 列出所有VST滤镜")
        print("  quit - 退出")
        print()
        
        while True:
            try:
                cmd = input("VST> ").strip().split()
                if not cmd:
                    continue
                
                if cmd[0] == "quit":
                    break
                elif cmd[0] == "add" and len(cmd) >= 3:
                    plugin_name = cmd[1]
                    filter_name = " ".join(cmd[2:])
                    controller.add_vst_filter(selected_source, filter_name, plugin_name)
                elif cmd[0] == "set" and len(cmd) >= 4:
                    filter_name = cmd[1]
                    param_name = cmd[2]
                    value = float(cmd[3])
                    controller.set_vst_parameter_by_name(selected_source, filter_name, param_name, value)
                elif cmd[0] == "get" and len(cmd) >= 2:
                    filter_name = " ".join(cmd[1:])
                    params = controller.get_vst_parameters(selected_source, filter_name)
                    for name, value in params.items():
                        print(f"   {name}: {value}")
                elif cmd[0] == "list":
                    vst_filters = controller.list_vst_filters(selected_source)
                    for filter_info in vst_filters:
                        status = "✅" if filter_info['enabled'] else "❌"
                        print(f"   {status} {filter_info['name']}")
                else:
                    print("❌ 无效命令")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
    
    finally:
        controller.disconnect()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        interactive_mode()
    else:
        main()
