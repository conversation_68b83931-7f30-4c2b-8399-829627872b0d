# VST控制器使用说明

## 🎯 功能概述

这个VST控制器可以通过WebSocket连接OBS，自动添加VST 2.x滤镜并实时控制参数，实现声音变化效果。

## 📋 支持的插件

- **Auburn Sounds Graillon 3-64** - 音调变化和声音处理
- **TSE_808_2.0_x64** - 失真和低音增强
- **TAL-Reverb-4-64** - 混响效果

## 🚀 快速开始

### 1. 环境准备

确保你已经安装了以下依赖：

```bash
pip install obsws-python websocket-client
```

### 2. OBS设置

1. **启动OBS Studio**
2. **启用WebSocket服务器**：
   - 工具 → WebSocket服务器设置
   - 勾选"启用WebSocket服务器"
   - 端口设为4455（默认）
   - 如果不需要密码，取消勾选"启用身份验证"

### 3. VST插件路径配置

在 `vst_complete_controller.py` 中修改插件路径：

```python
self.vst_plugins = {
    "Graillon": {
        "plugin_path": r"你的Graillon插件路径\Graillon 3.dll",
        # ...
    },
    "TSE808": {
        "plugin_path": r"你的TSE808插件路径\TSE808.dll",
        # ...
    },
    "TAL-Reverb": {
        "plugin_path": r"你的TAL-Reverb插件路径\TAL-Reverb-4.dll",
        # ...
    }
}
```

## 🎛️ 使用方法

### 方法1: 图形界面（推荐）

```bash
python vst_gui_controller.py
```

**操作步骤：**
1. 点击"连接OBS"
2. 选择音频源
3. 选择插件类型，输入滤镜名称，点击"添加滤镜"
4. 在滤镜列表中选择滤镜
5. 使用参数控制区域调整参数
6. 使用快速控制滑块实时调整音调和混合

### 方法2: 演示程序

```bash
python vst_controller_demo.py
```

这会运行一个自动演示，展示所有功能。

### 方法3: 交互模式

```bash
python vst_controller_demo.py interactive
```

提供命令行交互界面：
- `add Graillon 我的变声器` - 添加Graillon插件
- `set 我的变声器 pitch 5.0` - 设置音调参数
- `get 我的变声器` - 获取所有参数
- `list` - 列出所有VST滤镜
- `quit` - 退出

### 方法4: 编程接口

```python
from vst_complete_controller import VSTCompleteController

# 创建控制器
controller = VSTCompleteController()

# 连接OBS
if controller.connect():
    # 添加VST滤镜
    controller.add_vst_filter("麦克风", "Graillon变声", "Graillon")
    
    # 设置参数
    controller.set_vst_parameter_by_name("麦克风", "Graillon变声", "pitch", 5.0)
    
    # 断开连接
    controller.disconnect()
```

## 🔧 参数控制方法

### 方法1: 命名参数控制

适用于支持命名参数的插件：

```python
controller.set_vst_parameter_by_name(source_name, filter_name, "pitch", 5.0)
```

**常用参数名：**
- **Graillon**: `pitch`, `formant`, `mix`, `correction`
- **TSE808**: `drive`, `tone`, `level`
- **TAL-Reverb**: `roomsize`, `damping`, `wet`, `dry`

### 方法2: Chunk数据控制

适用于所有VST插件，通过修改二进制数据：

```python
controller.set_vst_parameter_by_chunk(source_name, filter_name, param_index, value)
```

**参数说明：**
- `param_index`: 参数在chunk中的索引（0, 1, 2...）
- `value`: 参数值（通常0.0-1.0）

## 🧬 Chunk数据初始化

如果VST插件的`chunk_data`为空，需要先初始化：

### 自动初始化
程序会自动尝试初始化，但可能不总是成功。

### 手动初始化（推荐）
1. 在OBS中双击VST滤镜
2. 等待插件界面加载
3. 调整任意一个参数（哪怕很小的调整）
4. 关闭插件界面
5. 现在chunk_data应该有数据了

## 🎵 实际应用示例

### 1. 实时变声
```python
import time
import math

# 创建正弦波音调变化
for i in range(100):
    pitch = 5.0 * math.sin(i * 0.1)  # -5到+5半音
    controller.set_vst_parameter_by_name("麦克风", "变声器", "pitch", pitch)
    time.sleep(0.1)
```

### 2. 音效切换
```python
# 正常声音
controller.set_vst_parameter_by_name("麦克风", "变声器", "pitch", 0.0)

# 机器人声音
controller.set_vst_parameter_by_name("麦克风", "变声器", "pitch", -8.0)
controller.set_vst_parameter_by_name("麦克风", "变声器", "formant", 80.0)

# 女声
controller.set_vst_parameter_by_name("麦克风", "变声器", "pitch", 5.0)
controller.set_vst_parameter_by_name("麦克风", "变声器", "formant", 120.0)
```

## ⚠️ 常见问题

### 1. 连接失败
- 确保OBS已启动
- 检查WebSocket服务器是否启用
- 确认端口号正确（默认4455）
- 如果设置了密码，需要在代码中配置

### 2. 找不到音频源
- 确保OBS中有音频源（麦克风、媒体源等）
- 音频源必须是实际的音频设备或文件

### 3. 添加滤镜失败
- 检查VST插件路径是否正确
- 确保插件文件存在且可访问
- 某些插件可能需要特定的运行环境

### 4. 参数设置无效
- 首先尝试命名参数方法
- 如果失败，使用chunk数据方法
- 确保chunk_data已初始化

### 5. chunk_data为空
- 手动打开VST插件界面
- 调整任意参数
- 关闭界面后重试

## 🔍 调试技巧

### 1. 查看当前参数
```python
params = controller.get_vst_parameters("麦克风", "变声器")
for name, value in params.items():
    print(f"{name}: {value}")
```

### 2. 分析chunk数据
程序会自动分析chunk数据结构，显示可能的参数值。

### 3. 监控日志
GUI界面和命令行都会显示详细的操作日志。

## 🎉 高级功能

### 1. 批量参数设置
```python
params = {
    "pitch": 3.0,
    "formant": 110.0,
    "mix": 80.0
}

for name, value in params.items():
    controller.set_vst_parameter_by_name("麦克风", "变声器", name, value)
```

### 2. 参数动画
```python
def animate_pitch(duration=10):
    start_time = time.time()
    while time.time() - start_time < duration:
        progress = (time.time() - start_time) / duration
        pitch = 5.0 * math.sin(progress * 4 * math.pi)
        controller.set_vst_parameter_by_name("麦克风", "变声器", "pitch", pitch)
        time.sleep(0.05)
```

### 3. 多插件协调
```python
# 同时控制多个插件
controller.set_vst_parameter_by_name("麦克风", "变声器", "pitch", 5.0)
controller.set_vst_parameter_by_name("麦克风", "混响", "wet", 40.0)
controller.set_vst_parameter_by_name("麦克风", "失真", "drive", 70.0)
```

## 📞 技术支持

如果遇到问题：
1. 检查OBS和插件是否正常工作
2. 查看程序日志输出
3. 确认插件路径和参数名称
4. 尝试手动初始化chunk数据

祝你使用愉快！🎵
