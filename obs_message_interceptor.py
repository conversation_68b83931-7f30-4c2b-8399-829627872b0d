#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBS WebSocket 消息拦截器
通过端口转发拦截并记录所有WebSocket通信
"""

import socket
import threading
import json
import time
from datetime import datetime
import tkinter as tk
from tkinter import scrolledtext, ttk, messagebox
import struct
import hashlib
import base64

class OBSMessageInterceptor:
    def __init__(self):
        """初始化消息拦截器"""
        self.root = tk.Tk()
        self.root.title("OBS WebSocket 消息拦截器")
        self.root.geometry("1200x800")
        
        self.is_intercepting = False
        self.server_socket = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 控制区域
        control_frame = ttk.Frame(self.root, padding="10")
        control_frame.pack(fill=tk.X)
        
        # 端口设置
        ttk.Label(control_frame, text="拦截端口:").pack(side=tk.LEFT)
        self.intercept_port_var = tk.StringVar(value="4456")
        ttk.Entry(control_frame, textvariable=self.intercept_port_var, width=8).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(control_frame, text="OBS端口:").pack(side=tk.LEFT)
        self.obs_port_var = tk.StringVar(value="4455")
        ttk.Entry(control_frame, textvariable=self.obs_port_var, width=8).pack(side=tk.LEFT, padx=(5, 10))
        
        # 控制按钮
        self.start_btn = ttk.Button(control_frame, text="开始拦截", command=self.start_intercepting)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="停止拦截", command=self.stop_intercepting, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态显示
        self.status_label = ttk.Label(control_frame, text="未拦截", foreground="red")
        self.status_label.pack(side=tk.RIGHT)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(self.root, text="拦截的WebSocket消息", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 说明信息
        info_frame = ttk.LabelFrame(self.root, text="使用说明", padding="5")
        info_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        info_text = """
拦截步骤：
1. 在OBS中踢出现有WebSocket连接（[::1]:3961）
2. 设置拦截端口（如4456）和OBS端口（4455）
3. 点击"开始拦截"
4. 修改目标程序连接地址为 ws://localhost:4456
5. 重新运行目标程序，所有消息都会被拦截并记录

注意：必须先断开现有连接，否则端口会冲突
        """
        ttk.Label(info_frame, text=info_text.strip(), justify=tk.LEFT).pack(anchor=tk.W)
        
    def log(self, message, msg_type="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        if msg_type == "SEND":
            prefix = "📤 客户端→OBS"
            color = "blue"
        elif msg_type == "RECV":
            prefix = "📥 OBS→客户端"
            color = "green"
        elif msg_type == "ERROR":
            prefix = "❌ 错误"
            color = "red"
        elif msg_type == "CONN":
            prefix = "🔗 连接"
            color = "purple"
        else:
            prefix = "ℹ️ 信息"
            color = "black"
            
        log_message = f"[{timestamp}] {prefix}: {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("日志已清空")
        
    def save_log(self):
        """保存日志到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"intercepted_messages_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
            self.log(f"日志已保存到: {filename}")
        except Exception as e:
            self.log(f"保存日志失败: {e}", "ERROR")
    
    def start_intercepting(self):
        """开始拦截"""
        if self.is_intercepting:
            return
            
        try:
            intercept_port = int(self.intercept_port_var.get())
            obs_port = int(self.obs_port_var.get())
        except ValueError:
            messagebox.showerror("错误", "端口号必须是数字")
            return
        
        self.is_intercepting = True
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.status_label.config(text=f"拦截中 (端口{intercept_port})", foreground="green")
        
        # 启动拦截服务器
        def run_interceptor():
            try:
                self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.server_socket.bind(('localhost', intercept_port))
                self.server_socket.listen(5)
                
                self.root.after(0, lambda: self.log(f"拦截服务器已启动，监听端口: {intercept_port}"))
                self.root.after(0, lambda: self.log(f"将转发到OBS端口: {obs_port}"))
                self.root.after(0, lambda: self.log("请修改目标程序连接地址为: ws://localhost:" + str(intercept_port)))
                
                while self.is_intercepting:
                    try:
                        client_socket, client_addr = self.server_socket.accept()
                        self.root.after(0, lambda addr=client_addr: self.log(f"客户端连接: {addr}", "CONN"))
                        
                        # 为每个客户端创建处理线程
                        client_thread = threading.Thread(
                            target=self.handle_client,
                            args=(client_socket, obs_port),
                            daemon=True
                        )
                        client_thread.start()
                        
                    except socket.error as e:
                        if self.is_intercepting:
                            self.root.after(0, lambda: self.log(f"接受连接时出错: {e}", "ERROR"))
                        break
                        
            except Exception as e:
                self.root.after(0, lambda: self.log(f"启动拦截服务器失败: {e}", "ERROR"))
                self.root.after(0, self.stop_intercepting)
        
        self.interceptor_thread = threading.Thread(target=run_interceptor, daemon=True)
        self.interceptor_thread.start()
    
    def handle_client(self, client_socket, obs_port):
        """处理客户端连接"""
        obs_socket = None
        try:
            # 连接到OBS
            obs_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            obs_socket.connect(('localhost', obs_port))
            self.log("已连接到OBS WebSocket服务器", "CONN")
            
            # 创建双向转发线程
            client_to_obs_thread = threading.Thread(
                target=self.forward_data,
                args=(client_socket, obs_socket, "客户端→OBS", "SEND"),
                daemon=True
            )
            obs_to_client_thread = threading.Thread(
                target=self.forward_data,
                args=(obs_socket, client_socket, "OBS→客户端", "RECV"),
                daemon=True
            )
            
            client_to_obs_thread.start()
            obs_to_client_thread.start()
            
            # 等待线程结束
            client_to_obs_thread.join()
            obs_to_client_thread.join()
            
        except Exception as e:
            self.log(f"处理客户端连接时出错: {e}", "ERROR")
        finally:
            if obs_socket:
                obs_socket.close()
            client_socket.close()
            self.log("客户端连接已关闭", "CONN")
    
    def forward_data(self, source_socket, target_socket, direction, msg_type):
        """转发数据并记录"""
        buffer = b""
        
        try:
            while self.is_intercepting:
                data = source_socket.recv(4096)
                if not data:
                    break
                
                # 转发数据
                target_socket.send(data)
                
                # 解析WebSocket帧
                buffer += data
                while buffer:
                    frame_data, remaining_buffer = self.parse_websocket_frame(buffer)
                    if frame_data is None:
                        break  # 需要更多数据
                    
                    buffer = remaining_buffer
                    
                    # 记录消息
                    if frame_data:
                        self.log_websocket_message(frame_data, direction, msg_type)
                        
        except Exception as e:
            if self.is_intercepting:
                self.log(f"转发数据时出错: {e}", "ERROR")
    
    def parse_websocket_frame(self, data):
        """解析WebSocket帧"""
        if len(data) < 2:
            return None, data  # 需要更多数据
        
        try:
            # 简单的WebSocket帧解析
            first_byte = data[0]
            second_byte = data[1]
            
            fin = (first_byte >> 7) & 1
            opcode = first_byte & 0x0f
            masked = (second_byte >> 7) & 1
            payload_length = second_byte & 0x7f
            
            offset = 2
            
            # 扩展载荷长度
            if payload_length == 126:
                if len(data) < offset + 2:
                    return None, data
                payload_length = struct.unpack('>H', data[offset:offset+2])[0]
                offset += 2
            elif payload_length == 127:
                if len(data) < offset + 8:
                    return None, data
                payload_length = struct.unpack('>Q', data[offset:offset+8])[0]
                offset += 8
            
            # 掩码
            if masked:
                if len(data) < offset + 4:
                    return None, data
                mask = data[offset:offset+4]
                offset += 4
            
            # 载荷数据
            if len(data) < offset + payload_length:
                return None, data  # 需要更多数据
            
            payload = data[offset:offset+payload_length]
            
            # 解除掩码
            if masked:
                payload = bytes(payload[i] ^ mask[i % 4] for i in range(len(payload)))
            
            # 只处理文本帧
            if opcode == 1:  # 文本帧
                try:
                    message = payload.decode('utf-8')
                    remaining = data[offset+payload_length:]
                    return message, remaining
                except UnicodeDecodeError:
                    pass
            
            # 跳过其他类型的帧
            remaining = data[offset+payload_length:]
            return "", remaining
            
        except Exception:
            # 解析失败，跳过这个数据包
            return "", data[1:] if len(data) > 1 else b""
    
    def log_websocket_message(self, message, direction, msg_type):
        """记录WebSocket消息"""
        if not message.strip():
            return
            
        try:
            # 尝试解析JSON并格式化
            json_data = json.loads(message)
            formatted_msg = json.dumps(json_data, indent=2, ensure_ascii=False)
            self.log(f"{direction}:\n{formatted_msg}", msg_type)
        except json.JSONDecodeError:
            # 如果不是JSON，直接记录
            self.log(f"{direction}: {message}", msg_type)
    
    def stop_intercepting(self):
        """停止拦截"""
        if not self.is_intercepting:
            return
            
        self.is_intercepting = False
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.status_label.config(text="未拦截", foreground="red")
        
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
            
        self.log("拦截已停止", "CONN")
    
    def run(self):
        """运行程序"""
        self.log("🚀 OBS WebSocket 消息拦截器已启动")
        self.log("💡 使用说明:")
        self.log("   1. 先在OBS中踢出现有WebSocket连接")
        self.log("   2. 点击'开始拦截'启动拦截服务器")
        self.log("   3. 修改目标程序连接到拦截端口")
        self.log("   4. 查看拦截到的所有WebSocket消息")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log("程序被用户中断")
        finally:
            self.stop_intercepting()

if __name__ == "__main__":
    interceptor = OBSMessageInterceptor()
    interceptor.run()
