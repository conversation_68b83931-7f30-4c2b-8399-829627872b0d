00:01:50.776: CPU Name: 12th Gen Intel(R) Core(TM) i5-12400F
00:01:50.776: CPU Speed: 2496MHz
00:01:50.776: Physical Cores: 6, Logical Cores: 12
00:01:50.776: Physical Memory: 32609MB Total, 16235MB Free
00:01:50.776: Windows Version: 10.0 Build 19045 (release: 22H2; revision: 6093; 64-bit)
00:01:50.776: Running as administrator: false
00:01:50.776: Windows 10/11 Gaming Features:
00:01:50.776: 	Game DVR: On
00:01:50.776: 	Game Mode: Probably On (no reg key set)
00:01:50.780: Sec. Software Status:
00:01:50.781: 	腾讯电脑管家系统防护: enabled (AV)
00:01:50.781: 	Microsoft Defender 防病毒: disabled (AV)
00:01:50.783: 	Windows 防火墙: disabled (FW)
00:01:50.783: Current Date/Time: 2025-07-29, 00:01:50
00:01:50.783: Browser Hardware Acceleration: true
00:01:50.783: Hide OBS windows from screen capture: false
00:01:50.783: Qt Version: 6.6.3 (runtime), 6.6.3 (compiled)
00:01:50.783: Portable mode: false
00:01:51.015: OBS 31.0.2 (64-bit, windows)
00:01:51.015: ---------------------------------
00:01:51.020: ---------------------------------
00:01:51.020: audio settings reset:
00:01:51.020: 	samples per sec: 48000
00:01:51.020: 	speakers:        2
00:01:51.020: 	max buffering:   960 milliseconds
00:01:51.020: 	buffering type:  dynamically increasing
00:01:51.022: ---------------------------------
00:01:51.022: Initializing D3D11...
00:01:51.022: Available Video Adapters: 
00:01:51.024: 	Adapter 0: NVIDIA GeForce RTX 3050
00:01:51.024: 	  Dedicated VRAM: 8432648192 (7.9 GiB)
00:01:51.024: 	  Shared VRAM:    17096949760 (15.9 GiB)
00:01:51.024: 	  PCI ID:         10de:2582
00:01:51.024: 	  HAGS Status:    Enabled (Default: No, Driver status: Supported)
00:01:51.024: 	  Driver Version: 32.0.15.6094
00:01:51.024: 	  output 0:
00:01:51.024: 	    name=SHUJIE
00:01:51.024: 	    pos={0, 0}
00:01:51.024: 	    size={1920, 1080}
00:01:51.024: 	    attached=true
00:01:51.024: 	    refresh=60
00:01:51.024: 	    bits_per_color=8
00:01:51.024: 	    space=RGB_FULL_G22_NONE_P709
00:01:51.024: 	    primaries=[r=(0.635742, 0.348633), g=(0.290039, 0.583984), b=(0.142578, 0.080078), wp=(0.313477, 0.329102)]
00:01:51.024: 	    relative_gamut_area=[709=0.932205, P3=0.687194, 2020=0.493016]
00:01:51.024: 	    sdr_white_nits=80
00:01:51.024: 	    nit_range=[min=0.500000, max=270.000000, max_full_frame=270.000000]
00:01:51.024: 	    dpi=96 (100%)
00:01:51.024: 	    id=\\?\DISPLAY#SHU2360#5&2da53d2&0&UID4353#{e6f07b5f-ee97-4a90-b076-33f57bf4eaa7}
00:01:51.024: 	    alt_id=\\.\DISPLAY1
00:01:51.025: 	Adapter 1: NVIDIA GeForce RTX 3050
00:01:51.025: 	  Dedicated VRAM: 8432648192 (7.9 GiB)
00:01:51.025: 	  Shared VRAM:    17096949760 (15.9 GiB)
00:01:51.025: 	  PCI ID:         10de:2582
00:01:51.025: 	  HAGS Status:    Enabled (Default: No, Driver status: Supported)
00:01:51.025: 	  Driver Version: 32.0.15.6094
00:01:51.025: Loading up D3D11 on adapter NVIDIA GeForce RTX 3050 (0)
00:01:51.121: D3D11 loaded successfully, feature level used: b000
00:01:51.121: DXGI increase maximum frame latency success
00:01:51.121: Hardware-Accelerated GPU Scheduling enabled on adapter!
00:01:51.121: D3D11 GPU priority setup failed (not admin?)
00:01:51.193: ---------------------------------
00:01:51.193: video settings reset:
00:01:51.193: 	base resolution:   1080x1920
00:01:51.193: 	output resolution: 1080x1920
00:01:51.193: 	downscale filter:  Bicubic
00:01:51.193: 	fps:               30/1
00:01:51.193: 	format:            NV12
00:01:51.193: 	YUV mode:          Rec. 709/Partial
00:01:51.193: NV12 texture support enabled
00:01:51.193: P010 texture support not available
00:01:51.194: Audio monitoring device:
00:01:51.194: 	name: 默认
00:01:51.194: 	id: default
00:01:51.261: ---------------------------------
00:01:51.261: Skipping module '../../obs-plugins/64bit/advanced-scene-switcher-lib.dll', not an OBS plugin
00:01:51.263: Module '../../obs-plugins/64bit/advanced-scene-switcher.dll' not loaded
00:01:51.264: No AJA devices found, skipping loading AJA UI plugin
00:01:51.264: Failed to initialize module 'aja-output-ui.dll'
00:01:51.265: No AJA devices found, skipping loading AJA plugin
00:01:51.265: Failed to initialize module 'aja.dll'
00:01:51.265: Skipping module '../../obs-plugins/64bit/chrome_elf.dll', not an OBS plugin
00:01:51.268: [CoreAudio encoder]: CoreAudio AAC encoder not installed on the system or couldn't be loaded
00:01:51.269: Failed to load 'en-US' text for module: 'decklink-captions.dll'
00:01:51.270: Failed to load 'en-US' text for module: 'decklink-output-ui.dll'
00:01:51.273: A DeckLink iterator could not be created.  The DeckLink drivers may not be installed
00:01:51.273: Failed to initialize module 'decklink.dll'
00:01:51.296: [AMF] Unable to load 'amfrt64.dll', error code 126.
00:01:51.299: [AMF] AMF Test failed due to one or more errors.
00:01:51.299: Failed to initialize module 'enc-amf.dll'
00:01:51.334: Skipping module '../../obs-plugins/64bit/libcef.dll', not an OBS plugin
00:01:51.334: Skipping module '../../obs-plugins/64bit/libEGL.dll', not an OBS plugin
00:01:51.334: Skipping module '../../obs-plugins/64bit/libGLESv2.dll', not an OBS plugin
00:01:51.335: [NVIDIA Audio Effects:] NVIDIA denoiser disabled, redistributable not found or could not be loaded.
00:01:51.336: Failed to get NVVideoEffects.dll version info size
00:01:51.337: [NVIDIA VIDEO FX]: FX disabled, redistributable not found or could not be loaded.
00:01:51.340: [obs-browser]: Version 2.24.5
00:01:51.340: [obs-browser]: CEF Version 127.0.6533.120 (runtime), 127.145.7+g2b7d20b+chromium-127.0.6533.120 (compiled)
00:01:51.341: [Composite Blur] loaded version 1.5.1
00:01:51.499: [obs-nvenc] NVENC version: 12.2 (compiled) / 12.2 (driver), CUDA driver version: 12.60, AV1 supported: false
00:01:51.540: [obs-websocket] [obs_module_load] you can haz websockets (Version: 5.5.5 | RPC Version: 1)
00:01:51.540: [obs-websocket] [obs_module_load] Qt version (compile-time): 6.6.3 | Qt version (run-time): 6.6.3
00:01:51.540: [obs-websocket] [obs_module_load] Linked ASIO Version: 103100
00:01:51.540: DEPRECATION: obs_frontend_get_global_config is deprecated. Read from global or user configuration explicitly instead.
00:01:51.547: [obs-websocket] [obs_module_load] Module loaded.
00:01:51.548: Failed to load 'zh-CN' text for module: 'obs-模糊-blur.dll'
00:01:51.549: [Composite Blur] loaded version 1.1.0
00:01:51.549: obs_register_source: Source 'obs_composite_blur' already exists!  Duplicate library?
00:01:51.553: [vlc-video]: VLC 3.0.18 Vetinari found, VLC video source enabled
00:01:51.564: Failed to load 'en-US' text for module: 'obsplus.dll'
00:01:51.839: [插件中心]: Loading Start -------------------------------------------------
00:01:51.839: DEPRECATION: obs_frontend_get_global_config is deprecated. Read from global or user configuration explicitly instead.
00:01:51.840: Failed to load 'en-US' text for module: 'C:\Users\<USER>\AppData\Roaming\obsplus\store\64bit\websocket.dll'
00:01:51.840: [OBSPLUS WEBSOCKET] : Server listen port is 3897
00:01:51.862: [插件中心]: current dll is last version
00:01:51.862: [插件中心]: Init Handler Success
00:01:51.862: [插件中心]: Check Uninstall Plugin List Success
00:01:51.927: DEPRECATION: obs_frontend_get_global_config is deprecated. Read from global or user configuration explicitly instead.
00:01:52.046: DEPRECATION: obs_frontend_get_global_config is deprecated. Read from global or user configuration explicitly instead.
00:01:52.171: [插件中心]: Plugin Update Success
00:01:52.172: [插件中心]: Load Plugin Success
00:01:52.173: [插件中心]: Starup Plugin Server Success
00:01:52.173: DEPRECATION: obs_frontend_get_global_config is deprecated. Read from global or user configuration explicitly instead.
00:01:52.174: [插件中心]: Loading End -------------------------------------------------
00:01:52.174: ---------------------------------
00:01:52.174:   Loaded Modules:
00:01:52.174:     C:\Users\<USER>\AppData\Roaming\obsplus\store\64bit\websocket.dll
00:01:52.175:     C:\Users\<USER>\AppData\Roaming\obsplus\store\64bit\store_qt6.dll
00:01:52.175:     obs-plugins-store.dll
00:01:52.175:     obsplus.dll
00:01:52.175:     win-wasapi.dll
00:01:52.175:     win-dshow.dll
00:01:52.175:     win-capture.dll
00:01:52.175:     vlc-video.dll
00:01:52.175:     text-freetype2.dll
00:01:52.175:     rtmp-services.dll
00:01:52.175:     obs-模糊-blur.dll
00:01:52.175:     obs-x264.dll
00:01:52.175:     obs-websocket.dll
00:01:52.175:     obs-webrtc.dll
00:01:52.175:     obs-vst.dll
00:01:52.175:     obs-transitions.dll
00:01:52.175:     obs-text.dll
00:01:52.175:     obs-qsv11.dll
00:01:52.175:     obs-outputs.dll
00:01:52.175:     obs-nvenc.dll
00:01:52.175:     obs-filters.dll
00:01:52.175:     obs-ffmpeg.dll
00:01:52.175:     obs-composite-blur.dll
00:01:52.175:     obs-browser.dll
00:01:52.175:     nv-filters.dll
00:01:52.175:     image-source.dll
00:01:52.175:     frontend-tools.dll
00:01:52.175:     decklink-output-ui.dll
00:01:52.175:     decklink-captions.dll
00:01:52.175:     coreaudio-encoder.dll
00:01:52.175: ---------------------------------
00:01:52.175: [obs-websocket] [obs_module_post_load] WebSocket server is enabled, starting...
00:01:52.175: [obs-websocket] [WebSocketServer::Start] Not locked to IPv4 bindings
00:01:52.175: [obs-websocket] [WebSocketServer::ServerRunner] IO thread started.
00:01:52.179: [obs-websocket] [WebSocketServer::Start] Server started successfully on port 4455. Possible connect address: ************
00:01:52.179: ---------------------------------
00:01:52.179: Available Encoders:
00:01:52.179:   Video Encoders:
00:01:52.179: 	- ffmpeg_svt_av1 (SVT-AV1)
00:01:52.179: 	- ffmpeg_aom_av1 (AOM AV1)
00:01:52.179: 	- obs_nvenc_h264_tex (NVIDIA NVENC H.264)
00:01:52.179: 	- obs_nvenc_hevc_tex (NVIDIA NVENC HEVC)
00:01:52.179: 	- obs_x264 (x264)
00:01:52.179:   Audio Encoders:
00:01:52.179: 	- ffmpeg_aac (FFmpeg AAC)
00:01:52.179: 	- ffmpeg_opus (FFmpeg Opus)
00:01:52.179: 	- ffmpeg_pcm_s16le (FFmpeg PCM (16位))
00:01:52.179: 	- ffmpeg_pcm_s24le (FFmpeg PCM (24位))
00:01:52.179: 	- ffmpeg_pcm_f32le (FFmpeg PCM (32位浮点))
00:01:52.179: 	- ffmpeg_alac (FFmpeg ALAC (24位))
00:01:52.179: 	- ffmpeg_flac (FFmpeg FLAC (16位))
00:01:52.179: ==== Startup complete ===============================================
00:01:52.217: All scene data cleared
00:01:52.217: ------------------------------------------------
00:01:52.220: [Media Source '媒体源']: settings:
00:01:52.220: 	input:                   E:/耳机视频.ts
00:01:52.220: 	input_format:            (null)
00:01:52.220: 	speed:                   112
00:01:52.220: 	is_looping:              yes
00:01:52.220: 	is_linear_alpha:         no
00:01:52.220: 	is_hw_decoding:          no
00:01:52.220: 	is_clear_on_media_end:   yes
00:01:52.220: 	restart_on_activate:     yes
00:01:52.220: 	close_when_inactive:     no
00:01:52.220: 	full_decode:             no
00:01:52.220: 	ffmpeg_options:          
00:01:52.228: User selected new VST plugin: 'C:/Program Files/VSTPlugins/Auburn Sounds Graillon 3-64.dll'
00:01:52.240: Switched to scene '场景'
00:01:52.240: ------------------------------------------------
00:01:52.240: Loaded scenes:
00:01:52.240: - scene '场景':
00:01:52.240:     - source: '媒体源' (ffmpeg_source)
00:01:52.240:         - monitoring: monitor and output
00:01:52.240:         - filter: 'VST 2.x 插件' (vst_filter)
00:01:52.240:         - filter: 'Composite Blur' (obs_composite_blur)
00:01:52.240: ------------------------------------------------
00:01:52.362: [插件中心]: OBS Finished Loading
00:01:52.362: signal_handler_connect: signal 'notice_status' not found
00:01:52.362: signal_handler_connect: signal 'notice_status' not found
00:01:52.362: signal_handler_connect: signal 'notice_status' not found
00:01:52.362: signal_handler_connect: signal 'notice_status' not found
00:01:52.362: signal_handler_connect: signal 'notice_status' not found
00:02:26.099: [obs-websocket] [WebSocketServer::onOpen] New WebSocket client has connected from [::1]:3961
00:02:33.558: signal_handler_connect: signal 'notice_status' not found
00:02:33.560: signal_handler_connect: signal 'notice_status' not found
00:02:33.562: signal_handler_connect: signal 'notice_status' not found
00:02:33.564: signal_handler_connect: signal 'notice_status' not found
00:02:33.566: signal_handler_connect: signal 'notice_status' not found
00:02:33.568: User selected new VST plugin: 'C:\Program Files\VSTPlugins\Auburn Sounds Graillon 3-64.dll'
00:02:33.580: User selected new VST plugin: 'C:\Program Files\VSTPlugins\TSE_808_2.0_x64.dll'
