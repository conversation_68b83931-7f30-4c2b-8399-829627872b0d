#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VST参数精确检查工具
专门用于检查VST参数的精确数值
"""

import json
import time
import websocket

class VSTParamChecker:
    def __init__(self):
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
    def connect_obs(self, url="ws://localhost:4455"):
        """连接到OBS"""
        try:
            print("🔄 正在连接到OBS...")
            self.ws = websocket.create_connection(url, timeout=5)
            
            # Hello消息
            hello_raw = self.ws.recv()
            hello_data = json.loads(hello_raw)
            print(f"📨 收到Hello: OBS v{hello_data.get('d', {}).get('obsWebSocketVersion', 'Unknown')}")
            
            if hello_data.get("op") != 0:
                raise ValueError("未收到Hello消息")
            
            rpc_version = hello_data.get("d", {}).get("rpcVersion", 1)
            
            # Identify消息
            identify_payload = {
                "op": 1,
                "d": {
                    "rpcVersion": rpc_version,
                    "eventSubscriptions": 33
                }
            }
            self.ws.send(json.dumps(identify_payload))
            
            # Identified消息
            identified_raw = self.ws.recv()
            identified_data = json.loads(identified_raw)
            
            if identified_data.get("op") != 2:
                raise ValueError("未收到Identified消息")
            
            self.is_connected = True
            print("✅ OBS连接成功！")
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
            
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            print(f"❌ 发送请求失败: {e}")
            return None
            
    def check_vst_params(self, source_name="媒体源", filter_name="Graillon"):
        """检查VST参数的精确值"""
        print(f"🔍 检查VST滤镜参数...")
        print(f"   源名称: {source_name}")
        print(f"   滤镜名称: {filter_name}")
        print("=" * 50)
        
        response_data = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })
        
        if not response_data:
            print("❌ 获取滤镜信息失败")
            return
            
        request_status = response_data.get("requestStatus", {})
        if not request_status.get("result"):
            print(f"❌ 获取失败: {request_status.get('comment', '未知错误')}")
            return
            
        filter_data = response_data.get("responseData", {})
        filter_settings = filter_data.get("filterSettings", {})
        
        print(f"📊 滤镜基本信息:")
        print(f"   类型: {filter_data.get('filterKind', 'Unknown')}")
        print(f"   启用: {filter_data.get('filterEnabled', False)}")
        print(f"   索引: {filter_data.get('filterIndex', 0)}")
        print()
        
        if not filter_settings:
            print("📭 无参数信息")
            return
            
        print(f"🎛️ 参数详细信息 (共 {len(filter_settings)} 个):")
        print("-" * 50)
        
        for param_name, param_value in filter_settings.items():
            param_type = type(param_value).__name__
            
            print(f"参数名: {param_name}")
            print(f"  值: {param_value}")
            print(f"  类型: {param_type}")
            
            if isinstance(param_value, float):
                print(f"  精确值: {repr(param_value)}")
                print(f"  二进制表示: {param_value.hex()}")
                
                # 检查是否是常见的VST范围值
                if 0.0 <= param_value <= 1.0:
                    print(f"  可能的映射值 (0-100): {param_value * 100:.6f}")
                    print(f"  可能的映射值 (-50到+50): {(param_value - 0.5) * 100:.6f}")
                    
            elif isinstance(param_value, str):
                if len(param_value) > 100:
                    print(f"  长度: {len(param_value)} 字符")
                    print(f"  前50字符: {param_value[:50]}...")
                else:
                    print(f"  完整值: {repr(param_value)}")
                    
            print()
            
    def test_param_modification(self, source_name="媒体源", filter_name="Graillon", param_name="Pitch", new_value=3.0):
        """测试参数修改"""
        print(f"🧪 测试参数修改...")
        print(f"   参数: {param_name}")
        print(f"   新值: {new_value}")
        
        # 先获取当前值
        print("1️⃣ 获取当前值...")
        current_data = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })
        
        if current_data and current_data.get("requestStatus", {}).get("result"):
            current_settings = current_data.get("responseData", {}).get("filterSettings", {})
            current_value = current_settings.get(param_name, "未找到")
            print(f"   当前值: {current_value} ({type(current_value).__name__})")
        else:
            print("   ❌ 无法获取当前值")
            return
            
        # 修改参数
        print("2️⃣ 修改参数...")
        modify_response = self.send_request("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": {
                param_name: new_value
            }
        })
        
        if modify_response and modify_response.get("requestStatus", {}).get("result"):
            print("   ✅ 修改成功")
        else:
            print("   ❌ 修改失败")
            return
            
        # 验证修改结果
        print("3️⃣ 验证修改结果...")
        time.sleep(0.5)  # 等待一下
        
        verify_data = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })
        
        if verify_data and verify_data.get("requestStatus", {}).get("result"):
            verify_settings = verify_data.get("responseData", {}).get("filterSettings", {})
            verify_value = verify_settings.get(param_name, "未找到")
            print(f"   修改后值: {verify_value} ({type(verify_value).__name__})")
            
            if isinstance(verify_value, (int, float)) and isinstance(new_value, (int, float)):
                if abs(verify_value - new_value) < 0.0001:
                    print("   ✅ 修改验证成功！")
                else:
                    print(f"   ⚠️ 值不完全匹配，差异: {abs(verify_value - new_value)}")
            else:
                print(f"   📊 修改结果: {verify_value}")
        else:
            print("   ❌ 无法验证修改结果")
            
    def disconnect(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
        self.is_connected = False
        print("👋 已断开连接")

def main():
    checker = VSTParamChecker()
    
    if not checker.connect_obs():
        return
        
    try:
        # 检查参数
        checker.check_vst_params()
        
        print("\n" + "="*60)
        
        # 测试修改参数
        response = input("\n是否要测试修改Pitch参数？(y/n): ")
        if response.lower() == 'y':
            try:
                new_value = float(input("请输入新的Pitch值: "))
                checker.test_param_modification(param_name="Pitch", new_value=new_value)
            except ValueError:
                print("❌ 输入的不是有效数字")
                
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    finally:
        checker.disconnect()

if __name__ == "__main__":
    main()
