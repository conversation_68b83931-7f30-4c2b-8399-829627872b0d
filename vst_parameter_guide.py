#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VST参数修改指南
详细说明VST插件参数的存储位置和修改方法
"""

import json
import base64
import struct
import websocket
import threading
import time

class VSTParameterGuide:
    def __init__(self):
        """初始化VST参数指南"""
        self.ws = None
        self.is_connected = False
        
    def connect_obs(self, ws_url="ws://localhost:4455"):
        """连接到OBS WebSocket"""
        try:
            self.ws = websocket.create_connection(ws_url, timeout=5)
            
            # 接收Hello消息
            hello_raw = self.ws.recv()
            hello_data = json.loads(hello_raw)
            print(f"✅ 连接成功: OBS WebSocket v{hello_data.get('d', {}).get('obsWebSocketVersion', 'Unknown')}")
            
            # 发送Identify消息
            identify_payload = {
                "op": 1,
                "d": {
                    "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                    "eventSubscriptions": 0
                }
            }
            self.ws.send(json.dumps(identify_payload))
            
            # 接收Identified消息
            identified_raw = self.ws.recv()
            identified_data = json.loads(identified_raw)
            
            if identified_data.get("op") == 2:
                self.is_connected = True
                print("🎉 OBS连接建立成功！")
                return True
            else:
                print("❌ 连接失败")
                return False
                
        except Exception as e:
            print(f"❌ 连接错误: {e}")
            return False
    
    def send_request(self, request_type, request_data=None):
        """发送请求到OBS"""
        if not self.is_connected or not self.ws:
            print("❌ 未连接到OBS")
            return None
            
        request_id = f"req-{int(time.time())}"
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            response_raw = self.ws.recv()
            response_data = json.loads(response_raw)
            return response_data.get("d", {})
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return None
    
    def explain_vst_parameters(self):
        """详细解释VST参数的存储和修改"""
        print("=" * 60)
        print("🎛️ VST插件参数存储和修改指南")
        print("=" * 60)
        
        print("\n📍 VST参数存储位置:")
        print("1. OBS配置文件 (scenes.json)")
        print("   - 存储基本信息：插件路径、启用状态等")
        print("   - 位置: %AppData%/obs-studio/basic/scenes/")
        
        print("\n2. VST插件内部状态 (chunk_data)")
        print("   - 存储所有插件参数的二进制数据")
        print("   - 以Base64编码格式存储在OBS中")
        print("   - 包含插件的完整状态信息")
        
        print("\n🔧 VST参数修改方法:")
        print("方法1: 直接参数修改")
        print("   - 适用于支持命名参数的VST插件")
        print("   - 使用SetSourceFilterSettings API")
        print("   - 参数名通常为: pitch, drive, mix, gain等")
        
        print("\n方法2: Chunk数据修改")
        print("   - 适用于所有VST插件")
        print("   - 需要了解参数在二进制数据中的位置")
        print("   - 修改chunk_data字段")
        
        print("\n方法3: 插件界面修改")
        print("   - 双击OBS中的VST滤镜")
        print("   - 直接在插件界面中调整参数")
        print("   - 最直观但无法自动化")
    
    def demonstrate_parameter_locations(self, source_name="媒体源", filter_name="VST 2.x 插件"):
        """演示参数位置查找"""
        print("\n🔍 演示VST参数位置查找:")
        print(f"   媒体源: {source_name}")
        print(f"   VST滤镜: {filter_name}")
        
        # 获取当前滤镜设置
        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })
        
        if not response or not response.get("requestStatus", {}).get("result"):
            print("❌ 无法获取滤镜信息")
            return
        
        filter_settings = response.get("responseData", {}).get("filterSettings", {})
        
        print("\n📊 当前滤镜参数:")
        for key, value in filter_settings.items():
            if key == "chunk_data":
                chunk_len = len(str(value)) if value else 0
                print(f"   {key}: [Base64数据，长度{chunk_len}]")
                if chunk_len > 0:
                    print("     ✅ 包含插件状态数据")
                else:
                    print("     ❌ 数据为空，插件可能未加载")
            elif key == "plugin_path":
                print(f"   {key}: {value}")
            else:
                print(f"   {key}: {value} ({type(value).__name__})")
    
    def demonstrate_direct_modification(self, source_name="媒体源", filter_name="VST 2.x 插件"):
        """演示直接参数修改"""
        print("\n🎯 演示直接参数修改:")
        
        # 常见的VST参数名称
        common_params = [
            ("pitch", 0.0, "音调偏移"),
            ("drive", 0.5, "驱动强度"),
            ("mix", 1.0, "混合比例"),
            ("gain", 0.0, "增益"),
            ("correction", 1.0, "音调修正"),
            ("formant", 0.0, "共振峰偏移")
        ]
        
        for param_name, test_value, description in common_params:
            print(f"\n🔧 测试参数: {param_name} ({description})")
            print(f"   设置值: {test_value}")
            
            response = self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: test_value
                }
            })
            
            if response and response.get("requestStatus", {}).get("result"):
                print("   ✅ 设置成功")
                
                # 验证设置结果
                verify_response = self.send_request("GetSourceFilter", {
                    "sourceName": source_name,
                    "filterName": filter_name
                })
                
                if verify_response and verify_response.get("requestStatus", {}).get("result"):
                    settings = verify_response.get("responseData", {}).get("filterSettings", {})
                    actual_value = settings.get(param_name, "未找到")
                    print(f"   📊 实际值: {actual_value}")
                    
                    if str(actual_value) == str(test_value):
                        print("   🎉 参数修改成功！")
                    else:
                        print("   ⚠️ 参数值可能被插件调整")
                else:
                    print("   ❌ 无法验证设置结果")
            else:
                error_msg = response.get("requestStatus", {}).get("comment", "未知错误") if response else "无响应"
                print(f"   ❌ 设置失败: {error_msg}")
            
            time.sleep(0.5)  # 短暂等待
    
    def demonstrate_chunk_analysis(self, source_name="媒体源", filter_name="VST 2.x 插件"):
        """演示chunk数据分析"""
        print("\n🧬 演示Chunk数据分析:")
        
        # 获取当前chunk数据
        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })
        
        if not response or not response.get("requestStatus", {}).get("result"):
            print("❌ 无法获取滤镜信息")
            return
        
        filter_settings = response.get("responseData", {}).get("filterSettings", {})
        chunk_data = filter_settings.get("chunk_data", "")
        
        if not chunk_data:
            print("❌ chunk_data为空，请先在插件界面中调整参数")
            return
        
        try:
            # 解码Base64数据
            decoded_data = base64.b64decode(chunk_data)
            print(f"📊 Chunk数据分析:")
            print(f"   Base64长度: {len(chunk_data)}")
            print(f"   二进制长度: {len(decoded_data)} 字节")
            
            # 分析前64字节的数据
            print(f"\n🔍 前64字节数据分析:")
            for i in range(0, min(64, len(decoded_data)), 4):
                if i + 4 <= len(decoded_data):
                    # 尝试解析为float
                    try:
                        float_val = struct.unpack('<f', decoded_data[i:i+4])[0]
                        print(f"   偏移{i:2d}: {float_val:8.4f} (float)")
                    except:
                        # 解析为整数
                        try:
                            int_val = struct.unpack('<I', decoded_data[i:i+4])[0]
                            print(f"   偏移{i:2d}: {int_val:8d} (uint)")
                        except:
                            print(f"   偏移{i:2d}: [无法解析]")
            
            print(f"\n💡 Chunk修改提示:")
            print(f"   1. 大多数VST参数存储为32位浮点数")
            print(f"   2. 参数通常按顺序存储在chunk中")
            print(f"   3. 需要通过实验确定具体参数位置")
            
        except Exception as e:
            print(f"❌ Chunk数据解析失败: {e}")
    
    def run_complete_demo(self):
        """运行完整演示"""
        print("🚀 启动VST参数修改完整演示...")
        
        if not self.connect_obs():
            return
        
        try:
            # 1. 解释参数存储
            self.explain_vst_parameters()
            
            # 2. 演示参数位置查找
            self.demonstrate_parameter_locations()
            
            # 3. 演示直接参数修改
            self.demonstrate_direct_modification()
            
            # 4. 演示chunk数据分析
            self.demonstrate_chunk_analysis()
            
            print("\n🎉 演示完成！")
            print("\n📝 总结:")
            print("1. VST参数存储在chunk_data中（Base64编码的二进制数据）")
            print("2. 可以通过SetSourceFilterSettings API直接修改命名参数")
            print("3. 也可以通过修改chunk_data来改变插件状态")
            print("4. 建议先尝试直接参数修改，失败后再考虑chunk修改")
            
        finally:
            if self.ws:
                self.ws.close()

if __name__ == "__main__":
    guide = VSTParameterGuide()
    guide.run_complete_demo()
