#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBS WebSocket 消息记录器
通过连接到OBS WebSocket来记录所有通信
"""

import asyncio
import websockets
import json
import time
from datetime import datetime
import tkinter as tk
from tkinter import scrolledtext, ttk, messagebox

class OBSWebSocketLogger:
    def __init__(self):
        """初始化WebSocket记录器"""
        self.root = tk.Tk()
        self.root.title("OBS WebSocket 消息记录器")
        self.root.geometry("1000x700")
        
        self.is_logging = False
        self.websocket = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 控制区域
        control_frame = ttk.Frame(self.root, padding="10")
        control_frame.pack(fill=tk.X)
        
        # 连接设置
        ttk.Label(control_frame, text="OBS地址:").pack(side=tk.LEFT)
        self.host_var = tk.StringVar(value="localhost")
        ttk.Entry(control_frame, textvariable=self.host_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(control_frame, text="端口:").pack(side=tk.LEFT)
        self.port_var = tk.StringVar(value="4455")
        ttk.Entry(control_frame, textvariable=self.port_var, width=8).pack(side=tk.LEFT, padx=(5, 10))
        
        # 控制按钮
        self.connect_btn = ttk.Button(control_frame, text="连接并记录", command=self.start_logging)
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.disconnect_btn = ttk.Button(control_frame, text="断开连接", command=self.stop_logging, state="disabled")
        self.disconnect_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=(0, 5))
        
        # 发送测试消息
        ttk.Button(control_frame, text="测试消息", command=self.send_test_message).pack(side=tk.LEFT, padx=(10, 5))
        
        # 状态显示
        self.status_label = ttk.Label(control_frame, text="未连接", foreground="red")
        self.status_label.pack(side=tk.RIGHT)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(self.root, text="WebSocket消息日志", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 说明信息
        info_frame = ttk.LabelFrame(self.root, text="使用说明", padding="5")
        info_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        info_text = """
使用方法：
1. 确保OBS WebSocket服务器已启动（端口4455）
2. 点击"连接并记录"开始监听
3. 运行目标程序，所有WebSocket消息都会被记录
4. 查看日志了解具体的通信内容

注意：此工具作为额外的客户端连接到OBS，不会干扰现有连接
        """
        ttk.Label(info_frame, text=info_text.strip(), justify=tk.LEFT).pack(anchor=tk.W)
        
    def log(self, message, msg_type="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        # 根据消息类型设置颜色标记
        if msg_type == "SEND":
            prefix = "📤 发送"
        elif msg_type == "RECV":
            prefix = "📥 接收"
        elif msg_type == "ERROR":
            prefix = "❌ 错误"
        elif msg_type == "CONN":
            prefix = "🔗 连接"
        else:
            prefix = "ℹ️ 信息"
            
        log_message = f"[{timestamp}] {prefix}: {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("日志已清空")
        
    def save_log(self):
        """保存日志到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"obs_websocket_log_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
            self.log(f"日志已保存到: {filename}")
        except Exception as e:
            self.log(f"保存日志失败: {e}", "ERROR")
    
    def start_logging(self):
        """开始记录"""
        if self.is_logging:
            return
            
        host = self.host_var.get().strip()
        try:
            port = int(self.port_var.get())
        except ValueError:
            messagebox.showerror("错误", "端口号必须是数字")
            return
        
        self.is_logging = True
        self.connect_btn.config(state="disabled")
        self.disconnect_btn.config(state="normal")
        self.status_label.config(text="连接中...", foreground="orange")
        
        # 在新线程中启动异步连接
        def run_logger():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                loop.run_until_complete(self.connect_and_log(host, port))
            except Exception as e:
                self.root.after(0, lambda: self.log(f"连接失败: {e}", "ERROR"))
                self.root.after(0, self.stop_logging)
        
        import threading
        self.logger_thread = threading.Thread(target=run_logger, daemon=True)
        self.logger_thread.start()
    
    async def connect_and_log(self, host, port):
        """连接并记录消息"""
        uri = f"ws://{host}:{port}"
        self.log(f"正在连接到 {uri}")
        
        try:
            async with websockets.connect(uri) as websocket:
                self.websocket = websocket
                self.root.after(0, lambda: self.status_label.config(text="已连接", foreground="green"))
                self.log("WebSocket连接已建立", "CONN")
                
                # 发送Hello响应（如果需要）
                try:
                    # 等待Hello消息
                    hello_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    self.log("收到Hello消息", "RECV")
                    self.log_message(hello_msg, "RECV")
                    
                    # 发送Identify消息
                    hello_data = json.loads(hello_msg)
                    if hello_data.get("op") == 0:  # Hello
                        identify_msg = {
                            "op": 1,
                            "d": {
                                "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                                "eventSubscriptions": 33  # 订阅所有事件
                            }
                        }
                        await websocket.send(json.dumps(identify_msg))
                        self.log("发送Identify消息", "SEND")
                        self.log_message(json.dumps(identify_msg, indent=2), "SEND")
                        
                        # 等待Identified消息
                        identified_msg = await websocket.recv()
                        self.log("收到Identified消息", "RECV")
                        self.log_message(identified_msg, "RECV")
                        
                except asyncio.TimeoutError:
                    self.log("未收到Hello消息，可能是旧版本协议", "INFO")
                
                # 持续监听消息
                async for message in websocket:
                    self.log_message(message, "RECV")
                    
        except Exception as e:
            self.root.after(0, lambda: self.log(f"连接错误: {e}", "ERROR"))
        finally:
            self.websocket = None
            self.root.after(0, self.stop_logging)
    
    def log_message(self, message, msg_type):
        """记录消息内容"""
        try:
            # 尝试解析JSON并格式化
            json_data = json.loads(message)
            formatted_msg = json.dumps(json_data, indent=2, ensure_ascii=False)
            self.root.after(0, lambda: self.log(f"\n{formatted_msg}", msg_type))
        except json.JSONDecodeError:
            # 如果不是JSON，直接记录
            self.root.after(0, lambda: self.log(message, msg_type))
    
    def send_test_message(self):
        """发送测试消息"""
        if not self.websocket:
            messagebox.showwarning("警告", "请先连接到OBS")
            return
        
        # 发送GetVersion请求
        test_msg = {
            "op": 6,
            "d": {
                "requestType": "GetVersion",
                "requestId": f"test-{int(time.time())}"
            }
        }
        
        asyncio.create_task(self.send_message(test_msg))
    
    async def send_message(self, message):
        """发送消息"""
        if self.websocket:
            try:
                msg_str = json.dumps(message)
                await self.websocket.send(msg_str)
                self.log("发送测试消息", "SEND")
                self.log_message(msg_str, "SEND")
            except Exception as e:
                self.log(f"发送消息失败: {e}", "ERROR")
    
    def stop_logging(self):
        """停止记录"""
        if not self.is_logging:
            return
            
        self.is_logging = False
        self.connect_btn.config(state="normal")
        self.disconnect_btn.config(state="disabled")
        self.status_label.config(text="未连接", foreground="red")
        
        if self.websocket:
            asyncio.create_task(self.websocket.close())
            
        self.log("连接已断开", "CONN")
    
    def run(self):
        """运行程序"""
        self.log("🚀 OBS WebSocket 消息记录器已启动")
        self.log("💡 连接到OBS后，所有WebSocket消息都会被记录")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log("程序被用户中断")
        finally:
            self.stop_logging()

if __name__ == "__main__":
    logger = OBSWebSocketLogger()
    logger.run()
