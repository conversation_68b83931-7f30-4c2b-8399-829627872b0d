#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBS诊断工具 - 检查音频源和VST插件状态
"""

import obsws_python as obs
import sys

def main():
    print("🔧 OBS VST插件诊断工具")
    print("=" * 50)
    
    try:
        # 连接OBS
        client = obs.ReqClient(host="localhost", port=4455, password=None, timeout=3)
        print("✅ 成功连接到OBS")
        
        # 1. 检查场景和音频源
        print(f"\n📋 1. 检查场景和音频源:")
        print("-" * 30)
        
        scene_list = client.get_scene_list()
        current_scene = scene_list.current_program_scene_name
        print(f"当前场景: {current_scene}")
        
        scene_items = client.get_scene_item_list(current_scene)
        
        media_source_found = False
        for item in scene_items.scene_items:
            source_name = item['sourceName']
            print(f"  📻 {source_name}")
            
            if source_name == "媒体源":
                media_source_found = True
                print(f"    ✅ 找到目标音频源")
                
                # 检查音频设置
                try:
                    audio_settings = client.get_input_audio_settings(input_name=source_name)
                    print(f"    🔊 音频监控: {audio_settings.monitor_type}")
                except:
                    print(f"    ⚠️ 无法获取音频设置")
        
        if not media_source_found:
            print("❌ 未找到'媒体源'，请检查名称是否正确")
            return
        
        # 2. 检查滤镜列表
        print(f"\n🎛️ 2. 检查滤镜列表:")
        print("-" * 30)
        
        try:
            filter_list = client.get_source_filter_list(source_name="媒体源")
            filters = filter_list.filters
            
            if not filters:
                print("❌ '媒体源'没有任何滤镜")
                return
            
            vst_filter_found = False
            for filter_info in filters:
                filter_name = filter_info['filterName']
                filter_type = filter_info['filterKind']
                filter_enabled = filter_info['filterEnabled']
                
                status = "✅ 启用" if filter_enabled else "❌ 禁用"
                print(f"  🎚️ {filter_name}")
                print(f"     类型: {filter_type}")
                print(f"     状态: {status}")
                
                if filter_name == "VST 2.x 插件":
                    vst_filter_found = True
                    
                    if not filter_enabled:
                        print(f"     ⚠️ VST滤镜被禁用，请在OBS中启用")
                    
                    # 检查滤镜详细设置
                    try:
                        filter_detail = client.get_source_filter(
                            source_name="媒体源",
                            filter_name=filter_name
                        )
                        
                        settings = filter_detail.filter_settings
                        print(f"     📊 滤镜参数:")
                        
                        for key, value in settings.items():
                            if key == 'chunk_data':
                                chunk_len = len(str(value)) if value else 0
                                print(f"       {key}: {chunk_len} 字符 {'(空)' if chunk_len == 0 else '(有数据)'}")
                            else:
                                print(f"       {key}: {value}")
                        
                        # 关键诊断
                        chunk_data = settings.get('chunk_data', '')
                        if not chunk_data:
                            print(f"     ❌ chunk_data为空 - 插件可能未正确加载")
                            print(f"     💡 建议:")
                            print(f"        1. 双击VST滤镜打开Graillon界面")
                            print(f"        2. 调整任意参数后关闭界面")
                            print(f"        3. 重新运行此诊断工具")
                        else:
                            print(f"     ✅ chunk_data有数据 - 插件已加载")
                            
                    except Exception as e:
                        print(f"     ❌ 无法获取滤镜详情: {e}")
                
                print()
            
            if not vst_filter_found:
                print("❌ 未找到'VST 2.x 插件'滤镜")
                print("💡 请在OBS中为'媒体源'添加VST 2.x插件滤镜")
                
        except Exception as e:
            print(f"❌ 获取滤镜列表失败: {e}")
        
        # 3. 检查音频电平
        print(f"\n🔊 3. 检查音频状态:")
        print("-" * 30)
        
        try:
            # 获取音频监控信息
            audio_settings = client.get_input_audio_settings(input_name="媒体源")
            print(f"音频监控类型: {audio_settings.monitor_type}")
            
            # 检查音量
            volume_info = client.get_input_volume(input_name="媒体源")
            volume_db = volume_info.volume_db
            volume_mul = volume_info.volume_mul
            
            print(f"音量: {volume_db:.1f}dB ({volume_mul:.2f})")
            
            if volume_db < -60:
                print("⚠️ 音量过低，可能没有音频信号")
            else:
                print("✅ 音量正常")
                
        except Exception as e:
            print(f"⚠️ 无法获取音频状态: {e}")
        
        # 4. 总结和建议
        print(f"\n📝 4. 诊断总结:")
        print("-" * 30)
        
        if media_source_found and vst_filter_found:
            print("✅ 基本配置正确")
            print("💡 如果chunk_data为空，请:")
            print("   1. 双击VST滤镜打开Graillon界面")
            print("   2. 调整音调或其他参数")
            print("   3. 关闭Graillon界面")
            print("   4. 重新运行简单测试.py")
        else:
            print("❌ 配置有问题，请检查:")
            if not media_source_found:
                print("   - '媒体源'不存在或名称不匹配")
            if not vst_filter_found:
                print("   - 'VST 2.x 插件'滤镜不存在")
            
    except Exception as e:
        print(f"❌ 连接或诊断失败: {e}")
        print("\n🔧 请检查:")
        print("1. OBS是否运行")
        print("2. WebSocket服务器是否启用")
        print("3. 端口和密码设置是否正确")

if __name__ == "__main__":
    main()
