import obsws_python as obs
import websocket
import json
import time
import base64
import struct
import threading
from typing import Dict, Any, Optional, List

class VSTCompleteController:
    """完整的VST控制器 - 支持添加滤镜、初始化和参数控制"""
    
    def __init__(self, host="localhost", port=4455, password=""):
        """初始化OBS WebSocket连接"""
        self.host = host
        self.port = port
        self.password = password
        self.client = None
        self.ws = None
        self.is_connected = False
        self.baseline_chunks = {}  # 存储各个VST的基准chunk数据
        
        # VST插件配置
        self.vst_plugins = {
            "Graillon": {
                "plugin_path": r"C:\Program Files\VSTPlugins\Auburn Sounds\Graillon 3\Graillon 3.dll",
                "filter_kind": "vst_filter",
                "default_params": {
                    "pitch": 0.0,
                    "formant": 100.0,
                    "mix": 100.0,
                    "correction": 50.0
                }
            },
            "TSE808": {
                "plugin_path": r"C:\Program Files\VSTPlugins\TSE\TSE808.dll", 
                "filter_kind": "vst_filter",
                "default_params": {
                    "drive": 50.0,
                    "tone": 50.0,
                    "level": 50.0
                }
            },
            "TAL-Reverb": {
                "plugin_path": r"C:\Program Files\VSTPlugins\TAL\TAL-Reverb-4.dll",
                "filter_kind": "vst_filter", 
                "default_params": {
                    "roomsize": 50.0,
                    "damping": 50.0,
                    "wet": 30.0,
                    "dry": 70.0
                }
            }
        }
        
    def connect(self) -> bool:
        """连接到OBS"""
        try:
            print("🔄 正在连接到OBS...")
            self.client = obs.ReqClient(
                host=self.host,
                port=self.port, 
                password=self.password,
                timeout=5
            )
            self.is_connected = True
            print("✅ 成功连接到OBS WebSocket")
            return True
            
        except Exception as e:
            print(f"❌ 连接OBS失败: {e}")
            print("请确保:")
            print("1. OBS已启动")
            print("2. WebSocket服务器已启用")
            print("3. 端口和密码设置正确")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.client:
            try:
                self.client.disconnect()
                print("✅ 已断开OBS连接")
            except:
                pass
        self.is_connected = False
        
    def get_audio_sources(self) -> List[str]:
        """获取所有音频源"""
        if not self.is_connected:
            return []
            
        try:
            response = self.client.get_source_list()
            sources = []
            
            for source in response.sources:
                # 检查是否为音频源
                source_name = source.get('sourceName', '')
                source_type = source.get('sourceKind', '')
                
                # 常见的音频源类型
                audio_types = [
                    'wasapi_input_capture',  # 音频输入捕获
                    'wasapi_output_capture', # 音频输出捕获
                    'ffmpeg_source',         # 媒体源
                    'dshow_input',           # 视频捕获设备
                    'pulse_input_capture',   # Linux音频输入
                    'pulse_output_capture'   # Linux音频输出
                ]
                
                if any(audio_type in source_type for audio_type in audio_types):
                    sources.append(source_name)
                    
            return sources
            
        except Exception as e:
            print(f"❌ 获取音频源失败: {e}")
            return []
    
    def add_vst_filter(self, source_name: str, filter_name: str, plugin_name: str) -> bool:
        """添加VST滤镜到指定音频源"""
        if not self.is_connected:
            print("❌ 未连接到OBS")
            return False
            
        if plugin_name not in self.vst_plugins:
            print(f"❌ 不支持的插件: {plugin_name}")
            return False
            
        plugin_config = self.vst_plugins[plugin_name]
        
        try:
            print(f"🔧 正在添加VST滤镜: {filter_name} ({plugin_name})")
            
            # 检查滤镜是否已存在
            try:
                existing = self.client.get_source_filter(
                    source_name=source_name,
                    filter_name=filter_name
                )
                print(f"⚠️ 滤镜 '{filter_name}' 已存在，将先删除")
                self.client.remove_source_filter(
                    source_name=source_name,
                    filter_name=filter_name
                )
                time.sleep(0.5)
            except:
                pass  # 滤镜不存在，继续创建
            
            # 创建VST滤镜
            filter_settings = {
                "plugin_path": plugin_config["plugin_path"],
                **plugin_config["default_params"]
            }
            
            response = self.client.create_source_filter(
                source_name=source_name,
                filter_name=filter_name,
                filter_kind=plugin_config["filter_kind"],
                filter_settings=filter_settings
            )
            
            print(f"✅ VST滤镜 '{filter_name}' 添加成功")
            
            # 等待插件加载
            time.sleep(2)
            
            # 初始化chunk数据
            self.initialize_vst_chunk(source_name, filter_name)
            
            return True
            
        except Exception as e:
            print(f"❌ 添加VST滤镜失败: {e}")
            return False
    
    def initialize_vst_chunk(self, source_name: str, filter_name: str) -> bool:
        """初始化VST插件的chunk数据"""
        print(f"🧬 正在初始化VST插件状态: {filter_name}")
        
        try:
            # 获取当前滤镜状态
            response = self.client.get_source_filter(
                source_name=source_name,
                filter_name=filter_name
            )
            
            filter_settings = response.filter_settings
            chunk_data = filter_settings.get('chunk_data', '')
            
            if not chunk_data:
                print("⚠️ chunk_data为空，尝试触发初始化...")
                
                # 方法1: 设置一些参数来触发状态保存
                init_params = {
                    "bypass": False,
                    "enabled": True
                }
                
                self.client.set_source_filter_settings(
                    source_name=source_name,
                    filter_name=filter_name,
                    filter_settings=init_params
                )
                
                time.sleep(1)
                
                # 再次检查
                response = self.client.get_source_filter(
                    source_name=source_name,
                    filter_name=filter_name
                )
                chunk_data = response.filter_settings.get('chunk_data', '')
                
            if chunk_data:
                # 保存基准chunk数据
                self.baseline_chunks[f"{source_name}_{filter_name}"] = chunk_data
                print(f"✅ chunk数据已初始化，长度: {len(chunk_data)}")
                
                # 分析chunk数据
                self.analyze_chunk_data(chunk_data)
                return True
            else:
                print("❌ 无法初始化chunk数据")
                print("💡 建议手动打开VST插件界面，调整任意参数后关闭")
                return False
                
        except Exception as e:
            print(f"❌ 初始化chunk数据失败: {e}")
            return False
    
    def analyze_chunk_data(self, chunk_data: str):
        """分析chunk数据结构"""
        try:
            decoded_data = base64.b64decode(chunk_data)
            print(f"🧬 Chunk数据分析:")
            print(f"   - 总长度: {len(decoded_data)} 字节")
            print(f"   - 可能的float参数数量: {len(decoded_data) // 4}")
            
            # 显示前10个可能的参数值
            if len(decoded_data) >= 40:  # 至少10个float
                print("   - 前10个可能的参数值:")
                for i in range(10):
                    try:
                        offset = i * 4
                        float_val = struct.unpack('<f', decoded_data[offset:offset+4])[0]
                        print(f"     参数 {i:2d}: {float_val:8.3f}")
                    except:
                        break
                        
        except Exception as e:
            print(f"❌ 分析chunk数据失败: {e}")

    def set_vst_parameter_by_chunk(self, source_name: str, filter_name: str,
                                   param_index: int, value: float) -> bool:
        """通过修改chunk数据设置VST参数"""
        chunk_key = f"{source_name}_{filter_name}"

        if chunk_key not in self.baseline_chunks:
            print(f"❌ 未找到基准chunk数据，请先初始化: {filter_name}")
            return False

        try:
            print(f"🎛️ 设置参数 {param_index} = {value}")

            # 解码基准数据
            decoded_data = bytearray(base64.b64decode(self.baseline_chunks[chunk_key]))

            # 检查参数索引是否有效
            if param_index * 4 + 4 > len(decoded_data):
                print(f"❌ 参数索引 {param_index} 超出范围 (最大: {len(decoded_data) // 4 - 1})")
                return False

            # 修改指定参数
            offset = param_index * 4
            struct.pack_into('<f', decoded_data, offset, value)

            # 重新编码
            new_chunk_data = base64.b64encode(decoded_data).decode('utf-8')

            # 发送到OBS
            response = self.client.set_source_filter_settings(
                source_name=source_name,
                filter_name=filter_name,
                filter_settings={"chunk_data": new_chunk_data}
            )

            print(f"✅ 参数设置成功: {param_index} = {value}")
            return True

        except Exception as e:
            print(f"❌ 设置参数失败: {e}")
            return False

    def set_vst_parameter_by_name(self, source_name: str, filter_name: str,
                                  param_name: str, value: float) -> bool:
        """通过参数名设置VST参数"""
        try:
            print(f"🎛️ 设置参数 {param_name} = {value}")

            response = self.client.set_source_filter_settings(
                source_name=source_name,
                filter_name=filter_name,
                filter_settings={param_name: value}
            )

            print(f"✅ 参数设置成功: {param_name} = {value}")
            return True

        except Exception as e:
            print(f"❌ 设置参数失败: {e}")
            # 如果命名参数失败，可以尝试chunk方法
            print("💡 尝试使用chunk方法设置参数...")
            return False

    def get_vst_parameters(self, source_name: str, filter_name: str) -> Dict[str, Any]:
        """获取VST插件的所有参数"""
        try:
            response = self.client.get_source_filter(
                source_name=source_name,
                filter_name=filter_name
            )

            filter_settings = response.filter_settings

            # 过滤掉系统参数
            system_params = ['plugin_path', 'chunk_data', 'chunk_hash']
            user_params = {k: v for k, v in filter_settings.items()
                          if k not in system_params}

            return user_params

        except Exception as e:
            print(f"❌ 获取参数失败: {e}")
            return {}

    def enable_vst_filter(self, source_name: str, filter_name: str, enabled: bool = True) -> bool:
        """启用/禁用VST滤镜"""
        try:
            response = self.client.set_source_filter_enabled(
                source_name=source_name,
                filter_name=filter_name,
                filter_enabled=enabled
            )

            status = "启用" if enabled else "禁用"
            print(f"✅ VST滤镜已{status}: {filter_name}")
            return True

        except Exception as e:
            print(f"❌ 设置滤镜状态失败: {e}")
            return False

    def remove_vst_filter(self, source_name: str, filter_name: str) -> bool:
        """移除VST滤镜"""
        try:
            response = self.client.remove_source_filter(
                source_name=source_name,
                filter_name=filter_name
            )

            # 清理基准数据
            chunk_key = f"{source_name}_{filter_name}"
            if chunk_key in self.baseline_chunks:
                del self.baseline_chunks[chunk_key]

            print(f"✅ VST滤镜已移除: {filter_name}")
            return True

        except Exception as e:
            print(f"❌ 移除滤镜失败: {e}")
            return False

    def update_plugin_path(self, plugin_name: str, new_path: str):
        """更新插件路径"""
        if plugin_name in self.vst_plugins:
            self.vst_plugins[plugin_name]["plugin_path"] = new_path
            print(f"✅ 已更新 {plugin_name} 路径: {new_path}")
        else:
            print(f"❌ 未知插件: {plugin_name}")

    def list_vst_filters(self, source_name: str) -> List[Dict[str, Any]]:
        """列出指定音频源的所有VST滤镜"""
        try:
            response = self.client.get_source_filter_list(source_name=source_name)
            vst_filters = []

            for filter_item in response.filters:
                filter_kind = filter_item.get('filterKind', '')
                if 'vst' in filter_kind.lower():
                    vst_filters.append({
                        'name': filter_item.get('filterName', ''),
                        'kind': filter_kind,
                        'enabled': filter_item.get('filterEnabled', False)
                    })

            return vst_filters

        except Exception as e:
            print(f"❌ 获取滤镜列表失败: {e}")
            return []
