#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBS连接测试程序
实现OBS的真实连接以及媒体源和滤镜的获取
"""

import sys
import json
import time
import websocket
import threading
from tkinter import *
from tkinter import ttk, messagebox, scrolledtext

class OBSTestConnection:
    def __init__(self):
        """初始化OBS测试连接程序"""
        self.root = Tk()
        self.root.title("OBS连接测试程序 v1.0")
        self.root.geometry("1200x900")
        
        # WebSocket连接相关
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        self.pending_requests = {}
        
        # 数据存储
        self.media_sources = []
        self.current_filters = []
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(W, E, N, S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=2)  # 媒体源和滤镜区域
        main_frame.rowconfigure(3, weight=3)  # 日志区域占更多空间
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="OBS连接", padding="5")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(W, E), pady=(0, 10))
        
        # 连接设置
        ttk.Label(conn_frame, text="WebSocket地址:").grid(row=0, column=0, sticky=W)
        self.ws_url_var = StringVar(value="ws://localhost:4455")
        ttk.Entry(conn_frame, textvariable=self.ws_url_var, width=30).grid(row=0, column=1, sticky=(W, E), padx=(5, 10))
        
        # 连接按钮和状态
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.toggle_connection)
        self.connect_btn.grid(row=0, column=2, padx=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="未连接", foreground="red")
        self.status_label.grid(row=0, column=3)
        
        conn_frame.columnconfigure(1, weight=1)
        
        # 操作区域
        action_frame = ttk.LabelFrame(main_frame, text="操作", padding="5")
        action_frame.grid(row=1, column=0, columnspan=2, sticky=(W, E), pady=(0, 10))
        
        ttk.Button(action_frame, text="获取媒体源", command=self.get_media_sources).pack(side=LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="获取滤镜", command=self.get_filters).pack(side=LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="初始化VST", command=self.initialize_vst_chunk).pack(side=LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="修改VST参数", command=self.show_vst_param_dialog).pack(side=LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="清空日志", command=self.clear_log).pack(side=LEFT, padx=(0, 5))
        
        # 媒体源选择
        ttk.Label(action_frame, text="选择媒体源:").pack(side=LEFT, padx=(20, 5))
        self.source_var = StringVar()
        self.source_combo = ttk.Combobox(action_frame, textvariable=self.source_var, width=20, state="readonly")
        self.source_combo.pack(side=LEFT, padx=(0, 5))
        
        # 主显示区域
        display_frame = ttk.Frame(main_frame)
        display_frame.grid(row=2, column=0, columnspan=2, sticky=(W, E, N, S))
        display_frame.columnconfigure(0, weight=1)
        display_frame.columnconfigure(1, weight=1)
        display_frame.rowconfigure(0, weight=1)
        
        # 左侧：媒体源列表
        sources_frame = ttk.LabelFrame(display_frame, text="媒体源列表", padding="5")
        sources_frame.grid(row=0, column=0, sticky=(W, E, N, S), padx=(0, 5))
        
        # 媒体源树形控件
        self.sources_tree = ttk.Treeview(sources_frame, columns=("type", "kind"), show="tree headings", height=10)
        self.sources_tree.heading("#0", text="名称")
        self.sources_tree.heading("type", text="类型")
        self.sources_tree.heading("kind", text="种类")
        self.sources_tree.column("#0", width=200)
        self.sources_tree.column("type", width=100)
        self.sources_tree.column("kind", width=150)
        
        # 滚动条
        sources_scroll = ttk.Scrollbar(sources_frame, orient=VERTICAL, command=self.sources_tree.yview)
        self.sources_tree.configure(yscrollcommand=sources_scroll.set)
        
        self.sources_tree.grid(row=0, column=0, sticky=(W, E, N, S))
        sources_scroll.grid(row=0, column=1, sticky=(N, S))
        sources_frame.columnconfigure(0, weight=1)
        sources_frame.rowconfigure(0, weight=1)
        
        # 右侧：滤镜列表
        filters_frame = ttk.LabelFrame(display_frame, text="滤镜列表", padding="5")
        filters_frame.grid(row=0, column=1, sticky=(W, E, N, S))
        
        # 滤镜树形控件
        self.filters_tree = ttk.Treeview(filters_frame, columns=("kind", "enabled"), show="tree headings", height=10)
        self.filters_tree.heading("#0", text="滤镜名称")
        self.filters_tree.heading("kind", text="滤镜类型")
        self.filters_tree.heading("enabled", text="状态")
        self.filters_tree.column("#0", width=200)
        self.filters_tree.column("kind", width=150)
        self.filters_tree.column("enabled", width=80)
        
        # 滚动条
        filters_scroll = ttk.Scrollbar(filters_frame, orient=VERTICAL, command=self.filters_tree.yview)
        self.filters_tree.configure(yscrollcommand=filters_scroll.set)
        
        self.filters_tree.grid(row=0, column=0, sticky=(W, E, N, S))
        filters_scroll.grid(row=0, column=1, sticky=(N, S))
        filters_frame.columnconfigure(0, weight=1)
        filters_frame.rowconfigure(0, weight=1)
        
        # 底部：日志区域
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="5")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(W, E, N, S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, wrap=WORD, font=('Consolas', 9))
        self.log_text.grid(row=0, column=0, sticky=(W, E, N, S))
        
        # 绑定事件
        self.sources_tree.bind("<<TreeviewSelect>>", self.on_source_select)
        self.filters_tree.bind("<<TreeviewSelect>>", self.on_filter_select)
        
        # 初始化日志
        self.log("🚀 OBS连接测试程序已启动")
        self.log("💡 请先连接到OBS，然后获取媒体源和滤镜信息")
        
    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(END, log_message)
        self.log_text.see(END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, END)
        self.log("📝 日志已清空")
        
    def toggle_connection(self):
        """切换连接状态"""
        if self.is_connected:
            self.disconnect_obs()
        else:
            self.connect_obs()
            
    def connect_obs(self):
        """连接到OBS"""
        self.log("🔄 正在连接到OBS...")
        self.connect_btn.config(state="disabled")
        
        def connect_thread():
            try:
                obs_ws_url = self.ws_url_var.get()
                self.ws = websocket.create_connection(obs_ws_url, timeout=5)
                self.log("✅ WebSocket连接已建立，等待Hello消息...")
                
                # 接收Hello消息 (Opcode 0)
                hello_raw = self.ws.recv()
                hello_data = json.loads(hello_raw)
                self.log(f"📨 收到Hello消息: OBS WebSocket v{hello_data.get('d', {}).get('obsWebSocketVersion', 'Unknown')}")
                
                if hello_data.get("op") != 0:
                    raise ValueError("收到的第一个消息不是Hello (Opcode 0)")
                
                rpc_version = hello_data.get("d", {}).get("rpcVersion", 1)
                authentication_required = hello_data.get("d", {}).get("authentication") is not None
                
                if authentication_required:
                    raise ConnectionAbortedError("OBS需要身份验证，请在OBS中禁用身份验证")
                
                # 发送Identify消息 (Opcode 1)
                identify_payload = {
                    "op": 1,
                    "d": {
                        "rpcVersion": rpc_version,
                        "eventSubscriptions": 33  # 订阅基本事件
                    }
                }
                self.ws.send(json.dumps(identify_payload))
                self.log("📤 已发送Identify消息")
                
                # 接收Identified消息 (Opcode 2)
                identified_raw = self.ws.recv()
                identified_data = json.loads(identified_raw)
                
                if identified_data.get("op") != 2:
                    raise ValueError("未收到Identified消息")
                
                self.log("✅ 已收到Identified消息，连接成功！")
                
                # 更新UI状态
                self.root.after(0, self.on_connect_success)
                
                # 启动消息监听
                self.start_message_listener()
                
            except Exception as e:
                self.root.after(0, self.on_connect_error, str(e))
                
        threading.Thread(target=connect_thread, daemon=True).start()
        
    def on_connect_success(self):
        """连接成功回调"""
        self.is_connected = True
        self.status_label.config(text="已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        self.log("🎉 OBS连接成功！现在可以获取媒体源和滤镜信息了")
        
    def on_connect_error(self, error_msg):
        """连接错误回调"""
        self.is_connected = False
        self.status_label.config(text="连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error_msg}")
        messagebox.showerror("连接错误", f"无法连接到OBS:\n{error_msg}")
        
    def disconnect_obs(self):
        """断开OBS连接"""
        self.log("🔄 正在断开连接...")
        self.is_connected = False
        
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.status_label.config(text="未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        self.log("✅ 已断开连接")
        
        # 清空数据
        self.clear_data()
        
    def clear_data(self):
        """清空所有数据"""
        # 清空树形控件
        for item in self.sources_tree.get_children():
            self.sources_tree.delete(item)
        for item in self.filters_tree.get_children():
            self.filters_tree.delete(item)
            
        # 清空下拉框
        self.source_combo['values'] = []
        self.source_var.set("")
        
        # 清空数据列表
        self.media_sources.clear()
        self.current_filters.clear()

    def start_message_listener(self):
        """启动消息监听线程"""
        def listen_thread():
            while self.is_connected and self.ws:
                try:
                    message_raw = self.ws.recv()
                    message_data = json.loads(message_raw)
                    self.handle_message(message_data)
                except websocket.WebSocketConnectionClosedException:
                    break
                except Exception as e:
                    if self.is_connected:
                        self.log(f"⚠️ 消息监听错误: {e}")

        threading.Thread(target=listen_thread, daemon=True).start()

    def handle_message(self, message_data):
        """处理接收到的消息"""
        op = message_data.get("op")

        if op == 7:  # RequestResponse
            self.handle_request_response(message_data.get("d", {}))
        elif op == 5:  # Event
            self.handle_event(message_data.get("d", {}))

    def handle_request_response(self, response_data):
        """处理请求响应"""
        request_id = response_data.get("requestId")
        if request_id in self.pending_requests:
            request_info = self.pending_requests.pop(request_id)
            callback = request_info.get("callback")
            if callback:
                callback(response_data)

    def handle_event(self, event_data):
        """处理事件"""
        event_type = event_data.get("eventType")
        # 这里可以处理各种OBS事件，如场景切换、源状态变化等
        # self.log(f"📡 收到事件: {event_type}")

    def send_request(self, request_type, request_data=None, callback=None):
        """发送请求到OBS"""
        if not self.is_connected or not self.ws:
            self.log("❌ 未连接到OBS，无法发送请求")
            return None

        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"

        payload = {
            "op": 6,  # Request
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }

        # 存储请求信息
        self.pending_requests[request_id] = {
            "type": request_type,
            "data": request_data,
            "callback": callback
        }

        try:
            self.ws.send(json.dumps(payload))
            return request_id
        except Exception as e:
            self.log(f"❌ 发送请求失败: {e}")
            if request_id in self.pending_requests:
                del self.pending_requests[request_id]
            return None

    def get_media_sources(self):
        """获取媒体源列表"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        self.log("🔍 正在获取媒体源列表...")

        def handle_response(response_data):
            self.root.after(0, self.process_media_sources, response_data)

        self.send_request("GetInputList", callback=handle_response)

    def process_media_sources(self, response_data):
        """处理媒体源响应"""
        request_status = response_data.get("requestStatus", {})
        if not request_status.get("result"):
            self.log(f"❌ 获取媒体源失败: {request_status.get('comment', '未知错误')}")
            return

        inputs_data = response_data.get("responseData", {})
        inputs_list = inputs_data.get("inputs", [])

        self.log(f"✅ 成功获取到 {len(inputs_list)} 个输入源")

        # 清空现有数据
        for item in self.sources_tree.get_children():
            self.sources_tree.delete(item)
        self.media_sources.clear()

        # 分类统计
        video_sources = []
        audio_sources = []
        media_sources = []
        other_sources = []

        for item in inputs_list:
            input_name = item.get("inputName", "")
            input_kind = item.get("inputKind", "")

            # 存储源信息
            self.media_sources.append(item)

            # 分类判断
            is_video = bool(item.get("videoTracks"))
            has_audio = item.get("audioTracks") is not None and len(item.get("audioTracks", [])) > 0
            is_media = input_kind in ['ffmpeg_source', 'vlc_source']
            is_audio_device = input_kind in [
                "wasapi_input_capture", "wasapi_output_capture",
                "coreaudio_input_capture", "coreaudio_output_capture",
                "pulse_input_capture", "pulse_output_capture"
            ]

            # 确定图标和分类
            if is_media:
                icon = "🎬"
                category = "媒体源"
                media_sources.append(input_name)
            elif is_video and has_audio:
                icon = "📹"
                category = "视频+音频"
                video_sources.append(input_name)
            elif is_video:
                icon = "🖼️"
                category = "视频源"
                video_sources.append(input_name)
            elif has_audio or is_audio_device:
                icon = "🎵"
                category = "音频源"
                audio_sources.append(input_name)
            else:
                icon = "⚪"
                category = "其他"
                other_sources.append(input_name)

            # 添加到树形控件
            item_id = self.sources_tree.insert('', 'end',
                text=f"{icon} {input_name}",
                values=(category, input_kind)
            )

        # 更新下拉框
        all_sources = [item.get("inputName", "") for item in inputs_list if item.get("inputName")]
        self.source_combo['values'] = all_sources
        if all_sources:
            self.source_combo.set(all_sources[0])

        # 统计信息
        self.log(f"📊 分类统计:")
        self.log(f"   🎬 媒体源: {len(media_sources)} 个")
        self.log(f"   📹 视频源: {len(video_sources)} 个")
        self.log(f"   🎵 音频源: {len(audio_sources)} 个")
        self.log(f"   ⚪ 其他源: {len(other_sources)} 个")

    def get_filters(self):
        """获取选中媒体源的滤镜"""
        source_name = self.source_var.get()
        if not source_name:
            messagebox.showwarning("警告", "请先选择一个媒体源")
            return

        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        self.log(f"🔍 正在获取媒体源 '{source_name}' 的滤镜...")

        def handle_response(response_data):
            self.root.after(0, self.process_filters, response_data, source_name)

        self.send_request("GetSourceFilterList",
                         {"sourceName": source_name},
                         callback=handle_response)

    def process_filters(self, response_data, source_name):
        """处理滤镜响应"""
        request_status = response_data.get("requestStatus", {})
        if not request_status.get("result"):
            self.log(f"❌ 获取滤镜失败: {request_status.get('comment', '未知错误')}")
            return

        filters_data = response_data.get("responseData", {})
        filters_list = filters_data.get("filters", [])

        self.log(f"✅ 媒体源 '{source_name}' 共有 {len(filters_list)} 个滤镜")

        # 清空现有滤镜数据
        for item in self.filters_tree.get_children():
            self.filters_tree.delete(item)
        self.current_filters.clear()

        if not filters_list:
            self.log("📭 该媒体源没有任何滤镜")
            return

        # 分类统计
        vst_filters = []
        audio_filters = []
        video_filters = []
        other_filters = []

        for filter_item in filters_list:
            filter_name = filter_item.get("filterName", "")
            filter_kind = filter_item.get("filterKind", "")
            filter_enabled = filter_item.get("filterEnabled", False)

            # 存储滤镜信息
            self.current_filters.append(filter_item)

            # 分类判断
            is_vst = 'vst' in filter_kind.lower()
            is_audio = any(keyword in filter_kind.lower() for keyword in
                          ['audio', 'noise', 'compressor', 'limiter', 'gain', 'eq'])
            is_video = any(keyword in filter_kind.lower() for keyword in
                          ['video', 'color', 'blur', 'crop', 'scale', 'chroma'])

            # 确定图标和分类
            if is_vst:
                icon = "🎛️"
                category = "VST插件"
                vst_filters.append(filter_name)
            elif is_audio:
                icon = "🔊"
                category = "音频滤镜"
                audio_filters.append(filter_name)
            elif is_video:
                icon = "🎨"
                category = "视频滤镜"
                video_filters.append(filter_name)
            else:
                icon = "⚙️"
                category = "其他滤镜"
                other_filters.append(filter_name)

            # 状态显示
            status = "✅ 启用" if filter_enabled else "❌ 禁用"

            # 添加到树形控件
            item_id = self.filters_tree.insert('', 'end',
                text=f"{icon} {filter_name}",
                values=(filter_kind, status)
            )

            # 为VST滤镜添加特殊标记
            if is_vst:
                self.filters_tree.set(item_id, 'is_vst', 'true')

        # 统计信息
        self.log(f"📊 滤镜分类统计:")
        self.log(f"   🎛️ VST插件: {len(vst_filters)} 个")
        self.log(f"   🔊 音频滤镜: {len(audio_filters)} 个")
        self.log(f"   🎨 视频滤镜: {len(video_filters)} 个")
        self.log(f"   ⚙️ 其他滤镜: {len(other_filters)} 个")

        # 显示VST滤镜详情
        if vst_filters:
            self.log("🎯 发现的VST插件:")
            for vst_name in vst_filters:
                self.log(f"   • {vst_name}")

    def on_source_select(self, event):
        """媒体源选择事件"""
        selection = self.sources_tree.selection()
        if selection:
            item = self.sources_tree.item(selection[0])
            source_text = item['text']
            # 提取源名称（去掉图标）
            source_name = source_text.split(' ', 1)[1] if ' ' in source_text else source_text
            self.source_var.set(source_name)
            self.log(f"📋 选择媒体源: {source_name}")

    def on_filter_select(self, event):
        """滤镜选择事件"""
        selection = self.filters_tree.selection()
        if selection:
            item = self.filters_tree.item(selection[0])
            filter_text = item['text']
            filter_kind = item['values'][0] if item['values'] else ""

            # 提取滤镜名称（去掉图标）
            filter_name = filter_text.split(' ', 1)[1] if ' ' in filter_text else filter_text

            self.log(f"🎛️ 选择滤镜: {filter_name} ({filter_kind})")

            # 如果是VST滤镜，获取详细信息
            if 'vst' in filter_kind.lower():
                self.get_filter_details(self.source_var.get(), filter_name)

    def get_filter_details(self, source_name, filter_name):
        """获取滤镜详细信息"""
        if not self.is_connected:
            return

        self.log(f"🔍 正在获取VST滤镜 '{filter_name}' 的详细信息...")

        def handle_response(response_data):
            self.root.after(0, self.process_filter_details, response_data, filter_name)

        self.send_request("GetSourceFilter",
                         {"sourceName": source_name, "filterName": filter_name},
                         callback=handle_response)

    def process_filter_details(self, response_data, filter_name):
        """处理滤镜详细信息响应"""
        request_status = response_data.get("requestStatus", {})
        if not request_status.get("result"):
            self.log(f"❌ 获取滤镜详情失败: {request_status.get('comment', '未知错误')}")
            return

        filter_data = response_data.get("responseData", {})
        filter_settings = filter_data.get("filterSettings", {})

        self.log(f"📊 VST滤镜 '{filter_name}' 详细信息:")
        self.log(f"   类型: {filter_data.get('filterKind', 'Unknown')}")
        self.log(f"   启用: {filter_data.get('filterEnabled', False)}")
        self.log(f"   索引: {filter_data.get('filterIndex', 0)}")

        if filter_settings:
            self.log(f"   参数 (共 {len(filter_settings)} 个):")
            for param_name, param_value in filter_settings.items():
                param_type = type(param_value).__name__
                if param_name == 'chunk_data':
                    chunk_len = len(str(param_value)) if param_value else 0
                    self.log(f"     • {param_name}: [长度{chunk_len}的字符串] ({param_type})")
                    if chunk_len == 0:
                        self.log(f"       ⚠️ chunk_data为空，插件可能未正确加载")
                        self.log(f"       💡 建议：双击VST滤镜打开界面，调整参数后关闭")
                    else:
                        self.log(f"       ✅ chunk_data有数据，插件已加载")
                elif param_name == 'plugin_path':
                    path_len = len(str(param_value)) if param_value else 0
                    self.log(f"     • {param_name}: [长度{path_len}的字符串] ({param_type})")
                else:
                    self.log(f"     • {param_name}: {param_value} ({param_type})")
        else:
            self.log("   📭 无可用参数")

        # 如果是VST滤镜，显示参数修改说明
        if 'vst' in filter_data.get('filterKind', '').lower():
            self.log("💡 VST参数修改方法:")
            self.log("   1. 直接参数修改 - 如果插件支持命名参数")
            self.log("   2. chunk_data修改 - 修改插件内部状态数据")
            self.log("   3. 插件界面修改 - 双击滤镜打开插件界面")

            # 检查chunk_data状态
            chunk_data = filter_settings.get('chunk_data', '')
            if not chunk_data:
                self.log("⚠️ chunk_data为空，建议先初始化VST插件状态")
                self.log("💡 点击'初始化VST'按钮或手动打开插件界面调整参数")

    def initialize_vst_chunk(self):
        """初始化VST插件的chunk数据"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        source_name = self.source_var.get()
        if not source_name:
            messagebox.showwarning("警告", "请先选择一个媒体源")
            return

        # 查找VST滤镜
        vst_filters = []
        for filter_item in self.current_filters:
            if 'vst' in filter_item.get('filterKind', '').lower():
                vst_filters.append(filter_item.get('filterName', ''))

        if not vst_filters:
            messagebox.showwarning("警告", "当前媒体源没有VST滤镜")
            return

        self.log("🔄 开始初始化VST插件状态...")

        for filter_name in vst_filters:
            self.log(f"🎛️ 初始化VST滤镜: {filter_name}")

            # 方法1: 尝试设置一个默认参数来触发状态保存
            self.log("   📝 尝试设置默认参数...")

            # 常见的VST参数默认值
            default_params = {
                "bypass": False,
                "enabled": True,
                "mix": 1.0,
                "gain": 0.0,
                "pitch": 0.0,
                "drive": 0.0
            }

            def handle_init_response(response_data):
                self.root.after(0, self.process_init_response, response_data, filter_name)

            # 尝试设置默认参数
            self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": default_params
            }, callback=handle_init_response)

    def process_init_response(self, response_data, filter_name):
        """处理初始化响应"""
        request_status = response_data.get("requestStatus", {})
        if request_status.get("result"):
            self.log(f"   ✅ {filter_name} 参数设置成功")

            # 等待一下再检查chunk状态
            self.root.after(1000, lambda: self.check_chunk_status(filter_name))
        else:
            error_msg = request_status.get('comment', '未知错误')
            self.log(f"   ⚠️ {filter_name} 参数设置失败: {error_msg}")
            self.log("   💡 建议手动操作:")
            self.log("      1. 双击VST滤镜打开插件界面")
            self.log("      2. 调整任意参数")
            self.log("      3. 关闭插件界面")
            self.log("      4. 重新获取滤镜信息")

    def check_chunk_status(self, filter_name):
        """检查chunk数据状态"""
        source_name = self.source_var.get()

        def handle_check_response(response_data):
            self.root.after(0, self.process_chunk_check, response_data, filter_name)

        self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        }, callback=handle_check_response)

    def process_chunk_check(self, response_data, filter_name):
        """处理chunk检查结果"""
        request_status = response_data.get("requestStatus", {})
        if not request_status.get("result"):
            self.log(f"   ❌ 无法检查{filter_name}的状态")
            return

        filter_settings = response_data.get("responseData", {}).get("filterSettings", {})
        chunk_data = filter_settings.get('chunk_data', '')

        if chunk_data:
            chunk_len = len(chunk_data)
            self.log(f"   🎉 {filter_name} chunk_data已生成！(长度: {chunk_len})")
            self.log("   ✅ 现在可以使用参数修改功能了")
        else:
            self.log(f"   ⚠️ {filter_name} chunk_data仍为空")
            self.log("   💡 请手动打开插件界面并调整参数")

    def show_vst_param_dialog(self):
        """显示VST参数修改对话框"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        source_name = self.source_var.get()
        if not source_name:
            messagebox.showwarning("警告", "请先选择一个媒体源")
            return

        # 创建参数修改对话框
        dialog = Toplevel(self.root)
        dialog.title("VST参数修改")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 主框架
        main_frame = ttk.Frame(dialog, padding="10")
        main_frame.pack(fill=BOTH, expand=True)

        # 源和滤镜信息
        info_frame = ttk.LabelFrame(main_frame, text="当前选择", padding="5")
        info_frame.pack(fill=X, pady=(0, 10))

        ttk.Label(info_frame, text=f"媒体源: {source_name}").pack(anchor=W)

        # 滤镜选择
        filter_frame = ttk.Frame(info_frame)
        filter_frame.pack(fill=X, pady=(5, 0))

        ttk.Label(filter_frame, text="VST滤镜:").pack(side=LEFT)
        filter_var = StringVar()
        filter_combo = ttk.Combobox(filter_frame, textvariable=filter_var, state="readonly")
        filter_combo.pack(side=LEFT, padx=(5, 0), fill=X, expand=True)

        # 获取VST滤镜列表
        vst_filters = []
        for filter_item in self.current_filters:
            if 'vst' in filter_item.get('filterKind', '').lower():
                vst_filters.append(filter_item.get('filterName', ''))

        filter_combo['values'] = vst_filters
        if vst_filters:
            filter_combo.set(vst_filters[0])

        # 参数修改方式选择
        method_frame = ttk.LabelFrame(main_frame, text="修改方式", padding="5")
        method_frame.pack(fill=X, pady=(0, 10))

        method_var = StringVar(value="direct")
        ttk.Radiobutton(method_frame, text="直接参数修改", variable=method_var, value="direct").pack(anchor=W)
        ttk.Radiobutton(method_frame, text="Chunk数据修改", variable=method_var, value="chunk").pack(anchor=W)

        # 参数输入区域
        param_frame = ttk.LabelFrame(main_frame, text="参数设置", padding="5")
        param_frame.pack(fill=BOTH, expand=True, pady=(0, 10))

        # 参数名称
        name_frame = ttk.Frame(param_frame)
        name_frame.pack(fill=X, pady=(0, 5))
        ttk.Label(name_frame, text="参数名称:").pack(side=LEFT)
        param_name_var = StringVar()
        ttk.Entry(name_frame, textvariable=param_name_var).pack(side=LEFT, padx=(5, 0), fill=X, expand=True)

        # 参数值
        value_frame = ttk.Frame(param_frame)
        value_frame.pack(fill=X, pady=(0, 5))
        ttk.Label(value_frame, text="参数值:").pack(side=LEFT)
        param_value_var = StringVar()
        ttk.Entry(value_frame, textvariable=param_value_var).pack(side=LEFT, padx=(5, 0), fill=X, expand=True)

        # 常用参数快捷按钮
        quick_frame = ttk.LabelFrame(param_frame, text="常用参数", padding="5")
        quick_frame.pack(fill=X, pady=(5, 0))

        def set_quick_param(name, value):
            param_name_var.set(name)
            param_value_var.set(str(value))

        # 第一行按钮
        row1 = ttk.Frame(quick_frame)
        row1.pack(fill=X, pady=(0, 2))
        ttk.Button(row1, text="Pitch=0", command=lambda: set_quick_param("pitch", 0.0)).pack(side=LEFT, padx=(0, 2))
        ttk.Button(row1, text="Pitch=3", command=lambda: set_quick_param("pitch", 3.0)).pack(side=LEFT, padx=(0, 2))
        ttk.Button(row1, text="Drive=0.5", command=lambda: set_quick_param("drive", 0.5)).pack(side=LEFT, padx=(0, 2))
        ttk.Button(row1, text="Mix=1.0", command=lambda: set_quick_param("mix", 1.0)).pack(side=LEFT, padx=(0, 2))

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=X)

        def apply_param():
            filter_name = filter_var.get()
            param_name = param_name_var.get().strip()
            param_value_str = param_value_var.get().strip()

            if not filter_name:
                messagebox.showwarning("警告", "请选择VST滤镜")
                return
            if not param_name:
                messagebox.showwarning("警告", "请输入参数名称")
                return
            if not param_value_str:
                messagebox.showwarning("警告", "请输入参数值")
                return

            try:
                param_value = float(param_value_str)
            except ValueError:
                messagebox.showwarning("警告", "参数值必须是数字")
                return

            # 执行参数修改
            if method_var.get() == "direct":
                self.modify_vst_param_direct(source_name, filter_name, param_name, param_value)
            else:
                self.modify_vst_param_chunk(source_name, filter_name, param_name, param_value)

            dialog.destroy()

        ttk.Button(button_frame, text="应用", command=apply_param).pack(side=RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=RIGHT)

    def modify_vst_param_direct(self, source_name, filter_name, param_name, param_value):
        """直接修改VST参数"""
        self.log(f"🎛️ 直接修改VST参数...")
        self.log(f"   媒体源: {source_name}")
        self.log(f"   滤镜: {filter_name}")
        self.log(f"   参数: {param_name} = {param_value}")

        def handle_response(response_data):
            self.root.after(0, self.process_param_modify_response, response_data, param_name, param_value)

        self.send_request("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": {
                param_name: param_value
            }
        }, callback=handle_response)

    def modify_vst_param_chunk(self, source_name, filter_name, param_name, param_value):
        """通过chunk数据修改VST参数"""
        self.log(f"🧬 通过chunk数据修改VST参数...")
        self.log(f"   媒体源: {source_name}")
        self.log(f"   滤镜: {filter_name}")
        self.log(f"   参数: {param_name} = {param_value}")
        self.log("⚠️ Chunk修改需要知道参数在二进制数据中的确切位置")
        self.log("💡 建议先尝试直接参数修改方式")

    def process_param_modify_response(self, response_data, param_name, param_value):
        """处理参数修改响应"""
        request_status = response_data.get("requestStatus", {})
        if request_status.get("result"):
            self.log(f"✅ 参数修改成功: {param_name} = {param_value}")
            # 重新获取滤镜信息验证修改结果
            source_name = self.source_var.get()
            selection = self.filters_tree.selection()
            if selection:
                item = self.filters_tree.item(selection[0])
                filter_text = item['text']
                filter_name = filter_text.split(' ', 1)[1] if ' ' in filter_text else filter_text
                self.get_filter_details(source_name, filter_name)
        else:
            error_msg = request_status.get('comment', '未知错误')
            self.log(f"❌ 参数修改失败: {error_msg}")
            self.log("💡 可能的原因:")
            self.log("   1. 参数名称不正确")
            self.log("   2. 参数值超出范围")
            self.log("   3. VST插件不支持该参数")

    def run(self):
        """运行程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log("👋 程序被用户中断")
        finally:
            if self.is_connected:
                self.disconnect_obs()

if __name__ == "__main__":
    print("🚀 启动OBS连接测试程序...")
    app = OBSTestConnection()
    app.run()
