[00:19:02.362] ℹ️ 信息: 🚀 OBS WebSocket 消息记录器已启动
[00:19:02.557] ℹ️ 信息: 💡 连接到OBS后，所有WebSocket消息都会被记录
[00:19:07.788] ℹ️ 信息: 正在连接到 ws://localhost:4455
[00:19:07.828] 🔗 连接: WebSocket连接已建立
[00:19:07.838] 📥 接收: 收到Hello消息
[00:19:07.844] 📥 接收: 
{
  "d": {
    "obsWebSocketVersion": "5.5.5",
    "rpcVersion": 1
  },
  "op": 0
}
[00:19:07.844] 📤 发送: 发送Identify消息
[00:19:07.852] 📤 发送: 
{
  "op": 1,
  "d": {
    "rpcVersion": 1,
    "eventSubscriptions": 33
  }
}
[00:19:07.852] 📥 接收: 收到Identified消息
[00:19:07.855] 📥 接收: 
{
  "d": {
    "negotiatedRpcVersion": 1
  },
  "op": 2
}

