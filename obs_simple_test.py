#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBS简单连接测试程序
实现OBS的真实连接以及媒体源和滤镜的获取（同步版本）
"""

import json
import time
import websocket
import threading
from tkinter import *
from tkinter import ttk, messagebox, scrolledtext

class OBSSimpleTest:
    def __init__(self):
        """初始化OBS简单测试程序"""
        self.root = Tk()
        self.root.title("OBS简单连接测试程序 v1.0")
        self.root.geometry("800x600")
        
        # WebSocket连接相关
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="OBS连接", padding="5")
        conn_frame.pack(fill=X, pady=(0, 10))
        
        # 连接设置
        conn_inner = ttk.Frame(conn_frame)
        conn_inner.pack(fill=X)
        
        ttk.Label(conn_inner, text="WebSocket地址:").pack(side=LEFT)
        self.ws_url_var = StringVar(value="ws://localhost:4455")
        ttk.Entry(conn_inner, textvariable=self.ws_url_var, width=25).pack(side=LEFT, padx=(5, 10))
        
        self.connect_btn = ttk.Button(conn_inner, text="连接OBS", command=self.toggle_connection)
        self.connect_btn.pack(side=LEFT, padx=(0, 10))
        
        self.status_label = ttk.Label(conn_inner, text="未连接", foreground="red")
        self.status_label.pack(side=LEFT)
        
        # 操作区域
        action_frame = ttk.LabelFrame(main_frame, text="操作", padding="5")
        action_frame.pack(fill=X, pady=(0, 10))
        
        action_inner = ttk.Frame(action_frame)
        action_inner.pack(fill=X)
        
        ttk.Button(action_inner, text="获取媒体源", command=self.get_media_sources).pack(side=LEFT, padx=(0, 5))
        ttk.Button(action_inner, text="获取滤镜", command=self.get_filters).pack(side=LEFT, padx=(0, 5))
        ttk.Button(action_inner, text="清空日志", command=self.clear_log).pack(side=LEFT, padx=(0, 5))
        
        # 媒体源选择
        ttk.Label(action_inner, text="媒体源:").pack(side=LEFT, padx=(20, 5))
        self.source_var = StringVar()
        self.source_combo = ttk.Combobox(action_inner, textvariable=self.source_var, width=20, state="readonly")
        self.source_combo.pack(side=LEFT, padx=(0, 5))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="5")
        log_frame.pack(fill=BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=WORD)
        self.log_text.pack(fill=BOTH, expand=True)
        
        # 初始化日志
        self.log("🚀 OBS简单连接测试程序已启动")
        self.log("💡 请先连接到OBS，然后获取媒体源和滤镜信息")
        
    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(END, log_message)
        self.log_text.see(END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, END)
        self.log("📝 日志已清空")
        
    def toggle_connection(self):
        """切换连接状态"""
        if self.is_connected:
            self.disconnect_obs()
        else:
            self.connect_obs()
            
    def connect_obs(self):
        """连接到OBS"""
        self.log("🔄 正在连接到OBS...")
        self.connect_btn.config(state="disabled")
        
        try:
            obs_ws_url = self.ws_url_var.get()
            self.ws = websocket.create_connection(obs_ws_url, timeout=5)
            self.log("✅ WebSocket连接已建立，等待Hello消息...")
            
            # 接收Hello消息 (Opcode 0)
            hello_raw = self.ws.recv()
            hello_data = json.loads(hello_raw)
            self.log(f"📨 收到Hello消息: OBS WebSocket v{hello_data.get('d', {}).get('obsWebSocketVersion', 'Unknown')}")
            
            if hello_data.get("op") != 0:
                raise ValueError("收到的第一个消息不是Hello (Opcode 0)")
            
            rpc_version = hello_data.get("d", {}).get("rpcVersion", 1)
            authentication_required = hello_data.get("d", {}).get("authentication") is not None
            
            if authentication_required:
                raise ConnectionAbortedError("OBS需要身份验证，请在OBS中禁用身份验证")
            
            # 发送Identify消息 (Opcode 1)
            identify_payload = {
                "op": 1,
                "d": {
                    "rpcVersion": rpc_version,
                    "eventSubscriptions": 33  # 订阅基本事件
                }
            }
            self.ws.send(json.dumps(identify_payload))
            self.log("📤 已发送Identify消息")
            
            # 接收Identified消息 (Opcode 2)
            identified_raw = self.ws.recv()
            identified_data = json.loads(identified_raw)
            
            if identified_data.get("op") != 2:
                raise ValueError("未收到Identified消息")
            
            self.log("✅ 已收到Identified消息，连接成功！")
            
            # 更新UI状态
            self.is_connected = True
            self.status_label.config(text="已连接", foreground="green")
            self.connect_btn.config(text="断开连接", state="normal")
            self.log("🎉 OBS连接成功！现在可以获取媒体源和滤镜信息了")
            
        except Exception as e:
            self.is_connected = False
            self.status_label.config(text="连接失败", foreground="red")
            self.connect_btn.config(text="连接OBS", state="normal")
            self.log(f"❌ 连接失败: {e}")
            messagebox.showerror("连接错误", f"无法连接到OBS:\n{e}")
            
    def disconnect_obs(self):
        """断开OBS连接"""
        self.log("🔄 正在断开连接...")
        self.is_connected = False
        
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.status_label.config(text="未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        self.log("✅ 已断开连接")
        
        # 清空数据
        self.source_combo['values'] = []
        self.source_var.set("")
        
    def send_request_and_get_response(self, request_type, request_data=None, timeout=5):
        """发送请求并等待响应（同步方式）"""
        if not self.is_connected or not self.ws:
            self.log("❌ 未连接到OBS，无法发送请求")
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,  # Request
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            self.log(f"📤 已发送请求: {request_type}")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        self.log(f"📥 收到响应: {request_type}")
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except Exception as e:
                    self.log(f"⚠️ 接收响应时出错: {e}")
                    continue
                    
            self.log(f"⏰ 请求超时: {request_type}")
            return None
            
        except Exception as e:
            self.log(f"❌ 发送请求失败: {e}")
            return None
            
    def get_media_sources(self):
        """获取媒体源列表"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return
            
        self.log("🔍 正在获取媒体源列表...")
        
        response_data = self.send_request_and_get_response("GetInputList")
        if not response_data:
            self.log("❌ 获取媒体源失败")
            return
            
        request_status = response_data.get("requestStatus", {})
        if not request_status.get("result"):
            self.log(f"❌ 获取媒体源失败: {request_status.get('comment', '未知错误')}")
            return
            
        inputs_data = response_data.get("responseData", {})
        inputs_list = inputs_data.get("inputs", [])
        
        self.log(f"✅ 成功获取到 {len(inputs_list)} 个输入源")
        
        # 更新下拉框
        source_names = []
        for item in inputs_list:
            input_name = item.get("inputName", "")
            input_kind = item.get("inputKind", "")
            
            if input_name:
                source_names.append(input_name)
                
                # 分类判断
                is_video = bool(item.get("videoTracks"))
                has_audio = item.get("audioTracks") is not None and len(item.get("audioTracks", [])) > 0
                is_media = input_kind in ['ffmpeg_source', 'vlc_source']
                
                # 确定类型
                if is_media:
                    source_type = "🎬 媒体源"
                elif is_video and has_audio:
                    source_type = "📹 视频+音频"
                elif is_video:
                    source_type = "🖼️ 视频源"
                elif has_audio:
                    source_type = "🎵 音频源"
                else:
                    source_type = "⚪ 其他"
                    
                self.log(f"   {source_type}: {input_name} ({input_kind})")
                
        self.source_combo['values'] = source_names
        if source_names:
            self.source_combo.set(source_names[0])
            
        self.log(f"📊 共找到 {len(source_names)} 个媒体源")
        
    def get_filters(self):
        """获取选中媒体源的滤镜"""
        source_name = self.source_var.get()
        if not source_name:
            messagebox.showwarning("警告", "请先选择一个媒体源")
            return
            
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return
            
        self.log(f"🔍 正在获取媒体源 '{source_name}' 的滤镜...")
        
        response_data = self.send_request_and_get_response("GetSourceFilterList", {"sourceName": source_name})
        if not response_data:
            self.log("❌ 获取滤镜失败")
            return
            
        request_status = response_data.get("requestStatus", {})
        if not request_status.get("result"):
            self.log(f"❌ 获取滤镜失败: {request_status.get('comment', '未知错误')}")
            return
            
        filters_data = response_data.get("responseData", {})
        filters_list = filters_data.get("filters", [])
        
        self.log(f"✅ 媒体源 '{source_name}' 共有 {len(filters_list)} 个滤镜")
        
        if not filters_list:
            self.log("📭 该媒体源没有任何滤镜")
            return
            
        # 显示滤镜信息
        vst_count = 0
        for filter_item in filters_list:
            filter_name = filter_item.get("filterName", "")
            filter_kind = filter_item.get("filterKind", "")
            filter_enabled = filter_item.get("filterEnabled", False)
            
            # 分类判断
            is_vst = 'vst' in filter_kind.lower()
            if is_vst:
                vst_count += 1
                icon = "🎛️"
                filter_type = "VST插件"
            elif any(keyword in filter_kind.lower() for keyword in ['audio', 'noise', 'compressor', 'limiter', 'gain', 'eq']):
                icon = "🔊"
                filter_type = "音频滤镜"
            elif any(keyword in filter_kind.lower() for keyword in ['video', 'color', 'blur', 'crop', 'scale', 'chroma']):
                icon = "🎨"
                filter_type = "视频滤镜"
            else:
                icon = "⚙️"
                filter_type = "其他滤镜"
                
            status = "✅ 启用" if filter_enabled else "❌ 禁用"
            self.log(f"   {icon} {filter_name} ({filter_kind}) - {status}")
            
            # 如果是VST滤镜，获取详细信息
            if is_vst:
                self.get_vst_details(source_name, filter_name)
                
        self.log(f"📊 滤镜统计: 总共 {len(filters_list)} 个，其中VST插件 {vst_count} 个")
        
    def get_vst_details(self, source_name, filter_name):
        """获取VST滤镜详细信息"""
        self.log(f"🔍 正在获取VST滤镜 '{filter_name}' 的详细信息...")
        
        response_data = self.send_request_and_get_response("GetSourceFilter", 
                                                          {"sourceName": source_name, "filterName": filter_name})
        if not response_data:
            self.log("❌ 获取VST滤镜详情失败")
            return
            
        request_status = response_data.get("requestStatus", {})
        if not request_status.get("result"):
            self.log(f"❌ 获取VST滤镜详情失败: {request_status.get('comment', '未知错误')}")
            return
            
        filter_data = response_data.get("responseData", {})
        filter_settings = filter_data.get("filterSettings", {})
        
        self.log(f"     📊 VST详情:")
        self.log(f"       类型: {filter_data.get('filterKind', 'Unknown')}")
        self.log(f"       启用: {filter_data.get('filterEnabled', False)}")
        self.log(f"       索引: {filter_data.get('filterIndex', 0)}")
        
        if filter_settings:
            self.log(f"       参数 (共 {len(filter_settings)} 个):")
            for param_name, param_value in filter_settings.items():
                param_type = type(param_value).__name__

                # 显示更详细的数值信息
                if isinstance(param_value, float):
                    self.log(f"         • {param_name}: {param_value} (精确值: {repr(param_value)}) ({param_type})")
                elif isinstance(param_value, str) and len(param_value) > 50:
                    self.log(f"         • {param_name}: [长度{len(param_value)}的字符串] ({param_type})")
                else:
                    self.log(f"         • {param_name}: {param_value} ({param_type})")
        else:
            self.log("       📭 无可用参数")
            
    def run(self):
        """运行程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log("👋 程序被用户中断")
        finally:
            if self.is_connected:
                self.disconnect_obs()

if __name__ == "__main__":
    print("🚀 启动OBS简单连接测试程序...")
    app = OBSSimpleTest()
    app.run()
