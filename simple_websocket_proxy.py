#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的WebSocket代理拦截器
使用纯socket实现，避免异步事件循环问题
"""

import socket
import threading
import json
import time
from datetime import datetime
import tkinter as tk
from tkinter import scrolledtext, ttk
import re
import base64
import hashlib

class SimpleWebSocketProxy:
    def __init__(self):
        """初始化WebSocket代理"""
        self.root = tk.Tk()
        self.root.title("简单WebSocket代理拦截器")
        self.root.geometry("1200x800")
        
        self.is_running = False
        self.server_socket = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 控制区域
        control_frame = ttk.Frame(self.root, padding="10")
        control_frame.pack(fill=tk.X)
        
        # 端口设置
        ttk.Label(control_frame, text="代理端口:").pack(side=tk.LEFT)
        self.proxy_port_var = tk.StringVar(value="4456")
        ttk.Entry(control_frame, textvariable=self.proxy_port_var, width=8).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(control_frame, text="OBS端口:").pack(side=tk.LEFT)
        self.obs_port_var = tk.StringVar(value="4455")
        ttk.Entry(control_frame, textvariable=self.obs_port_var, width=8).pack(side=tk.LEFT, padx=(5, 10))
        
        # 控制按钮
        self.start_btn = ttk.Button(control_frame, text="开始代理", command=self.start_proxy)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="停止代理", command=self.stop_proxy, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态显示
        self.status_label = ttk.Label(control_frame, text="未运行", foreground="red")
        self.status_label.pack(side=tk.RIGHT)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(self.root, text="拦截的WebSocket消息", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 说明信息
        info_frame = ttk.LabelFrame(self.root, text="使用说明", padding="5")
        info_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        info_text = """
使用步骤：
1. 在OBS中踢出现有WebSocket连接
2. 设置代理端口（如4456）和OBS端口（4455）
3. 点击"开始代理"
4. 修改目标程序连接地址为 ws://localhost:4456
5. 重新运行目标程序，查看拦截的消息

注意：这是一个简化版本，专门用于拦截WebSocket握手后的消息
        """
        ttk.Label(info_frame, text=info_text.strip(), justify=tk.LEFT).pack(anchor=tk.W)
        
    def log(self, message, msg_type="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        if msg_type == "SEND":
            prefix = "📤 客户端→OBS"
        elif msg_type == "RECV":
            prefix = "📥 OBS→客户端"
        elif msg_type == "ERROR":
            prefix = "❌ 错误"
        elif msg_type == "CONN":
            prefix = "🔗 连接"
        else:
            prefix = "ℹ️ 信息"
            
        log_message = f"[{timestamp}] {prefix}: {message}\n"
        
        def update_log():
            self.log_text.insert(tk.END, log_message)
            self.log_text.see(tk.END)
            
        if threading.current_thread() == threading.main_thread():
            update_log()
        else:
            self.root.after(0, update_log)
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("日志已清空")
        
    def save_log(self):
        """保存日志到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"websocket_proxy_log_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
            self.log(f"日志已保存到: {filename}")
        except Exception as e:
            self.log(f"保存日志失败: {e}", "ERROR")
    
    def start_proxy(self):
        """开始代理"""
        if self.is_running:
            return
            
        try:
            proxy_port = int(self.proxy_port_var.get())
            obs_port = int(self.obs_port_var.get())
        except ValueError:
            self.log("端口号必须是数字", "ERROR")
            return
        
        self.is_running = True
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.status_label.config(text=f"运行中 (端口{proxy_port})", foreground="green")
        
        # 启动代理服务器
        def run_proxy():
            try:
                self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.server_socket.bind(('localhost', proxy_port))
                self.server_socket.listen(5)
                
                self.log(f"代理服务器已启动，监听端口: {proxy_port}")
                self.log(f"将转发到OBS端口: {obs_port}")
                self.log("请修改目标程序连接地址为: ws://localhost:" + str(proxy_port))
                
                while self.is_running:
                    try:
                        client_socket, client_addr = self.server_socket.accept()
                        self.log(f"客户端连接: {client_addr}", "CONN")
                        
                        # 为每个客户端创建处理线程
                        client_thread = threading.Thread(
                            target=self.handle_client,
                            args=(client_socket, obs_port),
                            daemon=True
                        )
                        client_thread.start()
                        
                    except socket.error as e:
                        if self.is_running:
                            self.log(f"接受连接时出错: {e}", "ERROR")
                        break
                        
            except Exception as e:
                self.log(f"启动代理服务器失败: {e}", "ERROR")
                self.root.after(0, self.stop_proxy)
        
        self.proxy_thread = threading.Thread(target=run_proxy, daemon=True)
        self.proxy_thread.start()
    
    def handle_client(self, client_socket, obs_port):
        """处理客户端连接"""
        obs_socket = None
        try:
            # 连接到OBS
            obs_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            obs_socket.connect(('localhost', obs_port))
            self.log("已连接到OBS WebSocket服务器", "CONN")
            
            # 处理WebSocket握手
            self.handle_websocket_handshake(client_socket, obs_socket)
            
            # 创建双向转发线程
            client_to_obs_thread = threading.Thread(
                target=self.forward_websocket_data,
                args=(client_socket, obs_socket, "客户端→OBS", "SEND"),
                daemon=True
            )
            obs_to_client_thread = threading.Thread(
                target=self.forward_websocket_data,
                args=(obs_socket, client_socket, "OBS→客户端", "RECV"),
                daemon=True
            )
            
            client_to_obs_thread.start()
            obs_to_client_thread.start()
            
            # 等待线程结束
            client_to_obs_thread.join()
            obs_to_client_thread.join()
            
        except Exception as e:
            self.log(f"处理客户端连接时出错: {e}", "ERROR")
        finally:
            if obs_socket:
                obs_socket.close()
            client_socket.close()
            self.log("客户端连接已关闭", "CONN")
    
    def handle_websocket_handshake(self, client_socket, obs_socket):
        """处理WebSocket握手"""
        # 接收客户端握手请求
        request = client_socket.recv(4096).decode('utf-8')
        self.log("收到WebSocket握手请求", "CONN")
        
        # 转发给OBS
        obs_socket.send(request.encode('utf-8'))
        
        # 接收OBS响应
        response = obs_socket.recv(4096).decode('utf-8')
        self.log("收到WebSocket握手响应", "CONN")
        
        # 转发给客户端
        client_socket.send(response.encode('utf-8'))
        
        self.log("WebSocket握手完成", "CONN")
    
    def forward_websocket_data(self, source_socket, target_socket, direction, msg_type):
        """转发WebSocket数据并记录"""
        try:
            while self.is_running:
                data = source_socket.recv(4096)
                if not data:
                    break
                
                # 转发数据
                target_socket.send(data)
                
                # 尝试解析WebSocket消息
                try:
                    message = self.extract_websocket_message(data)
                    if message:
                        self.log_websocket_message(message, direction, msg_type)
                except:
                    # 如果解析失败，记录原始数据
                    self.log(f"{direction}: [二进制数据 {len(data)} 字节]", msg_type)
                        
        except Exception as e:
            if self.is_running:
                self.log(f"转发数据时出错: {e}", "ERROR")
    
    def extract_websocket_message(self, data):
        """提取WebSocket消息内容"""
        if len(data) < 2:
            return None
        
        # 简单的WebSocket帧解析
        second_byte = data[1]
        payload_length = second_byte & 0x7f
        
        if payload_length < 126:
            offset = 2
        elif payload_length == 126:
            if len(data) < 4:
                return None
            offset = 4
        else:
            if len(data) < 10:
                return None
            offset = 10
        
        # 检查是否有掩码
        if second_byte & 0x80:
            offset += 4
        
        if len(data) <= offset:
            return None
        
        # 提取载荷
        payload = data[offset:]
        
        try:
            # 尝试解码为UTF-8文本
            return payload.decode('utf-8')
        except:
            return None
    
    def log_websocket_message(self, message, direction, msg_type):
        """记录WebSocket消息"""
        if not message or not message.strip():
            return
            
        try:
            # 尝试解析JSON并格式化
            json_data = json.loads(message)
            formatted_msg = json.dumps(json_data, indent=2, ensure_ascii=False)
            self.log(f"{direction}:\n{formatted_msg}", msg_type)
        except json.JSONDecodeError:
            # 如果不是JSON，直接记录
            self.log(f"{direction}: {message}", msg_type)
    
    def stop_proxy(self):
        """停止代理"""
        if not self.is_running:
            return
            
        self.is_running = False
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.status_label.config(text="未运行", foreground="red")
        
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
            
        self.log("代理已停止", "CONN")
    
    def run(self):
        """运行程序"""
        self.log("🚀 简单WebSocket代理拦截器已启动")
        self.log("💡 这是一个简化版本，专门用于拦截WebSocket消息")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log("程序被用户中断")
        finally:
            self.stop_proxy()

if __name__ == "__main__":
    proxy = SimpleWebSocketProxy()
    proxy.run()
